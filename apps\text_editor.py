"""
Text Editor Application - Simple text editor with basic functionality
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import os

from ui.window import CustomWindow

class TextEditor:
    def __init__(self, parent, window_manager, window_id):
        self.parent = parent
        self.window_manager = window_manager
        self.window_id = window_id
        
        self.current_file = None
        self.is_modified = False
        self.font_size = 12
        self.font_family = "Consolas"
        
        self.create_window()
        self.create_interface()
        self.new_file()
        
    def create_window(self):
        """Create the text editor window"""
        self.custom_window = CustomWindow(
            self.parent,
            title="Text Editor",
            width=800,
            height=600
        )
        self.window = self.custom_window.get_window()
        self.content_frame = self.custom_window.get_content_frame()
        
        # Override close behavior to check for unsaved changes
        self.custom_window.on_close = self.on_close
        
    def create_interface(self):
        """Create the text editor interface"""
        # Menu bar
        self.create_menu()
        
        # Toolbar
        self.create_toolbar()
        
        # Main text area
        text_frame = tk.Frame(self.content_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Text widget with scrollbars
        self.text_widget = tk.Text(
            text_frame,
            wrap=tk.NONE,
            font=(self.font_family, self.font_size),
            undo=True,
            maxundo=50
        )
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.text_widget.yview)
        h_scrollbar = ttk.Scrollbar(text_frame, orient=tk.HORIZONTAL, command=self.text_widget.xview)
        self.text_widget.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack text widget and scrollbars
        self.text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Status bar
        self.status_bar = tk.Label(
            self.content_frame,
            text="Line 1, Column 1",
            relief=tk.SUNKEN,
            anchor=tk.W,
            bg='#f8f9fa'
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Bind events
        self.text_widget.bind('<KeyRelease>', self.on_text_change)
        self.text_widget.bind('<Button-1>', self.update_status)
        self.text_widget.bind('<Control-n>', lambda e: self.new_file())
        self.text_widget.bind('<Control-o>', lambda e: self.open_file())
        self.text_widget.bind('<Control-s>', lambda e: self.save_file())
        self.text_widget.bind('<Control-S>', lambda e: self.save_as_file())
        self.text_widget.bind('<Control-f>', lambda e: self.find_text())
        self.text_widget.bind('<Control-h>', lambda e: self.replace_text())
        
        self.text_widget.focus_set()
        
    def create_menu(self):
        """Create menu bar"""
        menubar = tk.Menu(self.window)
        self.window.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New", command=self.new_file, accelerator="Ctrl+N")
        file_menu.add_command(label="Open", command=self.open_file, accelerator="Ctrl+O")
        file_menu.add_separator()
        file_menu.add_command(label="Save", command=self.save_file, accelerator="Ctrl+S")
        file_menu.add_command(label="Save As", command=self.save_as_file, accelerator="Ctrl+Shift+S")
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_close)
        
        # Edit menu
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Undo", command=self.undo, accelerator="Ctrl+Z")
        edit_menu.add_command(label="Redo", command=self.redo, accelerator="Ctrl+Y")
        edit_menu.add_separator()
        edit_menu.add_command(label="Cut", command=self.cut, accelerator="Ctrl+X")
        edit_menu.add_command(label="Copy", command=self.copy, accelerator="Ctrl+C")
        edit_menu.add_command(label="Paste", command=self.paste, accelerator="Ctrl+V")
        edit_menu.add_separator()
        edit_menu.add_command(label="Select All", command=self.select_all, accelerator="Ctrl+A")
        edit_menu.add_separator()
        edit_menu.add_command(label="Find", command=self.find_text, accelerator="Ctrl+F")
        edit_menu.add_command(label="Replace", command=self.replace_text, accelerator="Ctrl+H")
        
        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Zoom In", command=self.zoom_in, accelerator="Ctrl++")
        view_menu.add_command(label="Zoom Out", command=self.zoom_out, accelerator="Ctrl+-")
        view_menu.add_command(label="Reset Zoom", command=self.reset_zoom)
        view_menu.add_separator()
        view_menu.add_command(label="Word Wrap", command=self.toggle_word_wrap)
        
    def create_toolbar(self):
        """Create toolbar"""
        toolbar = tk.Frame(self.content_frame, bg='#e9ecef', height=40)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        toolbar.pack_propagate(False)
        
        # File operations
        tk.Button(toolbar, text="New", command=self.new_file).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar, text="Open", command=self.open_file).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar, text="Save", command=self.save_file).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # Edit operations
        tk.Button(toolbar, text="Undo", command=self.undo).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar, text="Redo", command=self.redo).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # Find/Replace
        tk.Button(toolbar, text="Find", command=self.find_text).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar, text="Replace", command=self.replace_text).pack(side=tk.LEFT, padx=2)
        
    def new_file(self):
        """Create a new file"""
        if self.check_unsaved_changes():
            self.text_widget.delete(1.0, tk.END)
            self.current_file = None
            self.is_modified = False
            self.update_title()
            
    def open_file(self):
        """Open a file"""
        if self.check_unsaved_changes():
            file_path = filedialog.askopenfilename(
                title="Open File",
                filetypes=[
                    ("Text files", "*.txt"),
                    ("Python files", "*.py"),
                    ("All files", "*.*")
                ]
            )
            
            if file_path:
                try:
                    with open(file_path, 'r', encoding='utf-8') as file:
                        content = file.read()
                        
                    self.text_widget.delete(1.0, tk.END)
                    self.text_widget.insert(1.0, content)
                    self.current_file = file_path
                    self.is_modified = False
                    self.update_title()
                    
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to open file: {e}")
                    
    def save_file(self):
        """Save the current file"""
        if self.current_file:
            try:
                content = self.text_widget.get(1.0, tk.END + '-1c')
                with open(self.current_file, 'w', encoding='utf-8') as file:
                    file.write(content)
                    
                self.is_modified = False
                self.update_title()
                messagebox.showinfo("Success", "File saved successfully!")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save file: {e}")
        else:
            self.save_as_file()
            
    def save_as_file(self):
        """Save the file with a new name"""
        file_path = filedialog.asksaveasfilename(
            title="Save As",
            defaultextension=".txt",
            filetypes=[
                ("Text files", "*.txt"),
                ("Python files", "*.py"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            try:
                content = self.text_widget.get(1.0, tk.END + '-1c')
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                    
                self.current_file = file_path
                self.is_modified = False
                self.update_title()
                messagebox.showinfo("Success", "File saved successfully!")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save file: {e}")
                
    def check_unsaved_changes(self):
        """Check for unsaved changes and prompt user"""
        if self.is_modified:
            result = messagebox.askyesnocancel(
                "Unsaved Changes",
                "You have unsaved changes. Do you want to save them?"
            )
            
            if result is True:  # Yes
                self.save_file()
                return not self.is_modified  # Return True if save was successful
            elif result is False:  # No
                return True
            else:  # Cancel
                return False
        return True
        
    def on_text_change(self, event=None):
        """Handle text changes"""
        if not self.is_modified:
            self.is_modified = True
            self.update_title()
        self.update_status()
        
    def update_title(self):
        """Update window title"""
        filename = os.path.basename(self.current_file) if self.current_file else "Untitled"
        modified_indicator = "*" if self.is_modified else ""
        title = f"Text Editor - {filename}{modified_indicator}"
        self.custom_window.set_title(title)
        
    def update_status(self, event=None):
        """Update status bar"""
        cursor_pos = self.text_widget.index(tk.INSERT)
        line, column = cursor_pos.split('.')
        self.status_bar.config(text=f"Line {line}, Column {int(column) + 1}")
        
    def undo(self):
        """Undo last action"""
        try:
            self.text_widget.edit_undo()
        except tk.TclError:
            pass
            
    def redo(self):
        """Redo last undone action"""
        try:
            self.text_widget.edit_redo()
        except tk.TclError:
            pass
            
    def cut(self):
        """Cut selected text"""
        try:
            self.text_widget.event_generate("<<Cut>>")
        except tk.TclError:
            pass
            
    def copy(self):
        """Copy selected text"""
        try:
            self.text_widget.event_generate("<<Copy>>")
        except tk.TclError:
            pass
            
    def paste(self):
        """Paste text from clipboard"""
        try:
            self.text_widget.event_generate("<<Paste>>")
        except tk.TclError:
            pass
            
    def select_all(self):
        """Select all text"""
        self.text_widget.tag_add(tk.SEL, "1.0", tk.END)
        
    def find_text(self):
        """Find text dialog"""
        search_term = simpledialog.askstring("Find", "Enter text to find:")
        if search_term:
            # Simple find implementation
            start_pos = self.text_widget.search(search_term, "1.0", tk.END)
            if start_pos:
                end_pos = f"{start_pos}+{len(search_term)}c"
                self.text_widget.tag_remove(tk.SEL, "1.0", tk.END)
                self.text_widget.tag_add(tk.SEL, start_pos, end_pos)
                self.text_widget.mark_set(tk.INSERT, start_pos)
                self.text_widget.see(start_pos)
            else:
                messagebox.showinfo("Find", "Text not found")
                
    def replace_text(self):
        """Replace text dialog"""
        messagebox.showinfo("Replace", "Replace functionality not implemented yet")
        
    def zoom_in(self):
        """Increase font size"""
        self.font_size += 1
        self.text_widget.config(font=(self.font_family, self.font_size))
        
    def zoom_out(self):
        """Decrease font size"""
        if self.font_size > 8:
            self.font_size -= 1
            self.text_widget.config(font=(self.font_family, self.font_size))
            
    def reset_zoom(self):
        """Reset font size to default"""
        self.font_size = 12
        self.text_widget.config(font=(self.font_family, self.font_size))
        
    def toggle_word_wrap(self):
        """Toggle word wrap"""
        current_wrap = self.text_widget.cget('wrap')
        new_wrap = tk.NONE if current_wrap == tk.WORD else tk.WORD
        self.text_widget.config(wrap=new_wrap)
        
    def on_close(self):
        """Handle window close"""
        if self.check_unsaved_changes():
            return True  # Allow close
        return False  # Prevent close
        
    def cleanup(self):
        """Cleanup when closing"""
        pass
