"""
Settings Application - System configuration and preferences
"""

import tkinter as tk
from tkinter import ttk, messagebox, colorchooser
import json
import os

from ui.window import CustomWindow

class Settings:
    def __init__(self, parent, window_manager, window_id):
        self.parent = parent
        self.window_manager = window_manager
        self.window_id = window_id
        
        self.config_file = 'config/settings.json'
        self.load_settings()
        
        self.create_window()
        self.create_interface()
        
    def create_window(self):
        """Create the settings window"""
        self.custom_window = CustomWindow(
            self.parent,
            title="Settings",
            width=700,
            height=500
        )
        self.window = self.custom_window.get_window()
        self.content_frame = self.custom_window.get_content_frame()
        
    def load_settings(self):
        """Load settings from file"""
        try:
            with open(self.config_file, 'r') as f:
                self.settings = json.load(f)
        except FileNotFoundError:
            # Default settings
            self.settings = {
                "theme": "dark",
                "wallpaper": "#2c3e50",
                "taskbar_position": "bottom",
                "auto_hide_taskbar": False,
                "desktop_icons": True,
                "font_size": 12,
                "font_family": "Arial"
            }
            
    def save_settings(self):
        """Save settings to file"""
        try:
            os.makedirs('config', exist_ok=True)
            with open(self.config_file, 'w') as f:
                json.dump(self.settings, f, indent=4)
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {e}")
            return False
            
    def create_interface(self):
        """Create the settings interface"""
        # Create notebook for different setting categories
        notebook = ttk.Notebook(self.content_frame)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Appearance tab
        self.create_appearance_tab(notebook)
        
        # Desktop tab
        self.create_desktop_tab(notebook)
        
        # System tab
        self.create_system_tab(notebook)
        
        # About tab
        self.create_about_tab(notebook)
        
        # Buttons frame
        buttons_frame = tk.Frame(self.content_frame)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Button(
            buttons_frame,
            text="Apply",
            command=self.apply_settings,
            bg='#3498db',
            fg='white',
            padx=20
        ).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(
            buttons_frame,
            text="Cancel",
            command=self.window.destroy,
            padx=20
        ).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(
            buttons_frame,
            text="Reset to Defaults",
            command=self.reset_defaults,
            padx=20
        ).pack(side=tk.LEFT)
        
    def create_appearance_tab(self, notebook):
        """Create appearance settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Appearance")
        
        # Theme selection
        theme_frame = tk.LabelFrame(frame, text="Theme", padx=10, pady=10)
        theme_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.theme_var = tk.StringVar(value=self.settings.get("theme", "dark"))
        
        tk.Radiobutton(
            theme_frame,
            text="Dark Theme",
            variable=self.theme_var,
            value="dark"
        ).pack(anchor=tk.W)
        
        tk.Radiobutton(
            theme_frame,
            text="Light Theme",
            variable=self.theme_var,
            value="light"
        ).pack(anchor=tk.W)
        
        # Wallpaper color
        wallpaper_frame = tk.LabelFrame(frame, text="Desktop Background", padx=10, pady=10)
        wallpaper_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.wallpaper_var = tk.StringVar(value=self.settings.get("wallpaper", "#2c3e50"))
        
        wallpaper_entry_frame = tk.Frame(wallpaper_frame)
        wallpaper_entry_frame.pack(fill=tk.X)
        
        tk.Label(wallpaper_entry_frame, text="Color:").pack(side=tk.LEFT)
        
        self.wallpaper_entry = tk.Entry(
            wallpaper_entry_frame,
            textvariable=self.wallpaper_var,
            width=10
        )
        self.wallpaper_entry.pack(side=tk.LEFT, padx=5)
        
        tk.Button(
            wallpaper_entry_frame,
            text="Choose Color",
            command=self.choose_wallpaper_color
        ).pack(side=tk.LEFT, padx=5)
        
        # Color preview
        self.color_preview = tk.Label(
            wallpaper_entry_frame,
            text="   ",
            bg=self.wallpaper_var.get(),
            relief=tk.SUNKEN,
            width=3
        )
        self.color_preview.pack(side=tk.LEFT, padx=5)
        
        # Font settings
        font_frame = tk.LabelFrame(frame, text="Font Settings", padx=10, pady=10)
        font_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Font family
        font_family_frame = tk.Frame(font_frame)
        font_family_frame.pack(fill=tk.X, pady=2)
        
        tk.Label(font_family_frame, text="Font Family:").pack(side=tk.LEFT)
        
        self.font_family_var = tk.StringVar(value=self.settings.get("font_family", "Arial"))
        font_family_combo = ttk.Combobox(
            font_family_frame,
            textvariable=self.font_family_var,
            values=["Arial", "Helvetica", "Times New Roman", "Courier New", "Consolas"],
            state="readonly"
        )
        font_family_combo.pack(side=tk.LEFT, padx=10)
        
        # Font size
        font_size_frame = tk.Frame(font_frame)
        font_size_frame.pack(fill=tk.X, pady=2)
        
        tk.Label(font_size_frame, text="Font Size:").pack(side=tk.LEFT)
        
        self.font_size_var = tk.IntVar(value=self.settings.get("font_size", 12))
        font_size_spin = tk.Spinbox(
            font_size_frame,
            from_=8,
            to=24,
            textvariable=self.font_size_var,
            width=5
        )
        font_size_spin.pack(side=tk.LEFT, padx=10)
        
    def create_desktop_tab(self, notebook):
        """Create desktop settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Desktop")
        
        # Desktop icons
        icons_frame = tk.LabelFrame(frame, text="Desktop Icons", padx=10, pady=10)
        icons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.desktop_icons_var = tk.BooleanVar(value=self.settings.get("desktop_icons", True))
        
        tk.Checkbutton(
            icons_frame,
            text="Show desktop icons",
            variable=self.desktop_icons_var
        ).pack(anchor=tk.W)
        
        # Taskbar settings
        taskbar_frame = tk.LabelFrame(frame, text="Taskbar", padx=10, pady=10)
        taskbar_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Taskbar position
        position_frame = tk.Frame(taskbar_frame)
        position_frame.pack(fill=tk.X, pady=2)
        
        tk.Label(position_frame, text="Position:").pack(side=tk.LEFT)
        
        self.taskbar_position_var = tk.StringVar(value=self.settings.get("taskbar_position", "bottom"))
        
        position_combo = ttk.Combobox(
            position_frame,
            textvariable=self.taskbar_position_var,
            values=["top", "bottom"],
            state="readonly"
        )
        position_combo.pack(side=tk.LEFT, padx=10)
        
        # Auto-hide taskbar
        self.auto_hide_var = tk.BooleanVar(value=self.settings.get("auto_hide_taskbar", False))
        
        tk.Checkbutton(
            taskbar_frame,
            text="Auto-hide taskbar",
            variable=self.auto_hide_var
        ).pack(anchor=tk.W, pady=5)
        
    def create_system_tab(self, notebook):
        """Create system settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="System")
        
        # System information (read-only)
        info_frame = tk.LabelFrame(frame, text="System Information", padx=10, pady=10)
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        try:
            import platform
            import psutil
            
            info_text = f"""Operating System: {platform.system()} {platform.release()}
Architecture: {platform.architecture()[0]}
Processor: {platform.processor()}
Python Version: {platform.python_version()}
Total Memory: {psutil.virtual_memory().total / (1024**3):.1f} GB
Available Memory: {psutil.virtual_memory().available / (1024**3):.1f} GB"""
            
        except ImportError:
            info_text = "System information not available (psutil not installed)"
            
        info_label = tk.Label(
            info_frame,
            text=info_text,
            justify=tk.LEFT,
            anchor=tk.W
        )
        info_label.pack(fill=tk.X)
        
        # Performance settings
        perf_frame = tk.LabelFrame(frame, text="Performance", padx=10, pady=10)
        perf_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(
            perf_frame,
            text="Performance settings will be available in future versions.",
            fg="gray"
        ).pack(anchor=tk.W)
        
    def create_about_tab(self, notebook):
        """Create about tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="About")
        
        # About information
        about_frame = tk.Frame(frame)
        about_frame.pack(expand=True, fill=tk.BOTH, padx=20, pady=20)
        
        # Logo/Title
        title_label = tk.Label(
            about_frame,
            text="🐍 Python OS",
            font=("Arial", 24, "bold"),
            fg="#3498db"
        )
        title_label.pack(pady=10)
        
        # Version info
        version_label = tk.Label(
            about_frame,
            text="Version 1.0",
            font=("Arial", 12)
        )
        version_label.pack()
        
        # Description
        desc_text = """A Linux-like desktop environment built with Python and Tkinter.

Features:
• File Manager with basic file operations
• Web Browser with text-based rendering
• Terminal Emulator with command support
• Text Editor with syntax highlighting
• System Settings and configuration
• Window management and taskbar

Built with Python, Tkinter, and love ❤️

© 2024 Python OS Project"""
        
        desc_label = tk.Label(
            about_frame,
            text=desc_text,
            justify=tk.CENTER,
            font=("Arial", 10)
        )
        desc_label.pack(pady=20)
        
    def choose_wallpaper_color(self):
        """Open color chooser for wallpaper"""
        color = colorchooser.askcolor(
            initialcolor=self.wallpaper_var.get(),
            title="Choose Desktop Background Color"
        )
        
        if color[1]:  # If a color was selected
            self.wallpaper_var.set(color[1])
            self.color_preview.config(bg=color[1])
            
    def apply_settings(self):
        """Apply the current settings"""
        # Update settings dictionary
        self.settings["theme"] = self.theme_var.get()
        self.settings["wallpaper"] = self.wallpaper_var.get()
        self.settings["taskbar_position"] = self.taskbar_position_var.get()
        self.settings["auto_hide_taskbar"] = self.auto_hide_var.get()
        self.settings["desktop_icons"] = self.desktop_icons_var.get()
        self.settings["font_family"] = self.font_family_var.get()
        self.settings["font_size"] = self.font_size_var.get()
        
        # Save settings
        if self.save_settings():
            messagebox.showinfo(
                "Settings Applied",
                "Settings have been applied successfully!\n\nSome changes may require restarting Python OS to take effect."
            )
            
            # Try to refresh the desktop if possible
            try:
                # This would need to be implemented in the main OS class
                pass
            except:
                pass
                
    def reset_defaults(self):
        """Reset all settings to defaults"""
        if messagebox.askyesno("Reset Settings", "Are you sure you want to reset all settings to defaults?"):
            # Reset to default values
            self.theme_var.set("dark")
            self.wallpaper_var.set("#2c3e50")
            self.taskbar_position_var.set("bottom")
            self.auto_hide_var.set(False)
            self.desktop_icons_var.set(True)
            self.font_family_var.set("Arial")
            self.font_size_var.set(12)
            
            # Update color preview
            self.color_preview.config(bg="#2c3e50")
            
    def cleanup(self):
        """Cleanup when closing"""
        pass
