# Python OS

A Linux-like desktop environment built with Python and Tkinter that runs on Windows as an executable file.

## Features

- **Desktop Environment**: Complete desktop interface with taskbar, system tray, and desktop icons
- **File Manager**: Browse and manage files and directories with a graphical interface
- **Web Browser**: Simple text-based web browser with bookmark support
- **Terminal Emulator**: Command-line interface with built-in commands and external program support
- **Text Editor**: Full-featured text editor with syntax highlighting and file operations
- **System Settings**: Configure appearance, desktop, and system preferences
- **Window Management**: Multi-window support with minimize, maximize, and close operations

## Screenshots

*Screenshots will be available after first run*

## Installation

### Option 1: Run from Source

1. **Clone or download** this repository
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
3. **Run Python OS**:
   ```bash
   python main.py
   ```

### Option 2: Build Executable

1. **Install build dependencies**:
   ```bash
   pip install pyinstaller psutil pillow requests
   ```
2. **Run the build script**:
   ```bash
   python build.py
   ```
3. **Run the executable**:
   ```bash
   dist/PythonOS.exe
   ```

## System Requirements

- **Operating System**: Windows 7 or later
- **Python**: 3.7+ (for running from source)
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 100MB free space
- **Display**: 1024x768 minimum resolution

## Keyboard Shortcuts

- `Ctrl+Alt+T` - Open Terminal
- `Ctrl+Alt+F` - Open File Manager
- `Ctrl+Alt+B` - Open Web Browser
- `Alt+F4` - Exit Python OS

## Applications

### File Manager
- Browse files and directories
- Basic file operations (copy, cut, paste, delete)
- Quick access to common directories
- File properties and information

### Web Browser
- Text-based web page rendering
- Bookmark management
- History navigation
- External browser integration

### Terminal
- Command-line interface
- Built-in commands (cd, ls, pwd, etc.)
- External program execution
- Command history

### Text Editor
- Syntax highlighting
- Find and replace
- Multiple file support
- Customizable fonts and themes

### Settings
- Theme customization
- Desktop configuration
- System information
- Performance settings

## Development

### Project Structure

```
python_os/
├── main.py              # Main entry point
├── requirements.txt     # Python dependencies
├── build.py            # Build script for executable
├── config/             # Configuration files
├── apps/               # Application modules
│   ├── file_manager.py
│   ├── browser.py
│   ├── terminal.py
│   ├── text_editor.py
│   └── settings.py
├── ui/                 # UI components
│   ├── desktop.py
│   ├── taskbar.py
│   └── window.py
├── system/             # System services
│   ├── window_manager.py
│   ├── app_launcher.py
│   └── process_manager.py
└── resources/          # Icons and themes
```

### Adding New Applications

1. Create a new Python file in the `apps/` directory
2. Implement your application class with the required interface:
   ```python
   class MyApp:
       def __init__(self, parent, window_manager, window_id):
           # Initialize your app
           pass
       
       def cleanup(self):
           # Cleanup when closing
           pass
   ```
3. Register your app in `system/app_launcher.py`

### Customizing Themes

Edit `config/settings.json` to customize:
- Colors and themes
- Font settings
- Desktop layout
- Taskbar configuration

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Known Issues

- Web browser only displays text content (no images or JavaScript)
- Some file operations may require administrator privileges
- Performance may vary on older systems

## Future Enhancements

- [ ] Full HTML/CSS web browser support
- [ ] Plugin system for applications
- [ ] Network file sharing
- [ ] System monitoring tools
- [ ] Package manager
- [ ] Virtual desktop support

## License

This project is open source and available under the MIT License.

## Support

For issues, questions, or contributions:
- Create an issue on GitHub
- Check the documentation
- Review existing issues and discussions

---

**Python OS** - Bringing the Linux desktop experience to Windows with Python! 🐍
