"""
Custom Window Class - Base class for application windows
"""

import tkinter as tk
from tkinter import ttk

class CustomWindow:
    def __init__(self, parent, title="Application", width=800, height=600, resizable=True):
        self.parent = parent
        self.title = title
        self.width = width
        self.height = height
        
        self.create_window()
        self.setup_window_controls()
        
    def create_window(self):
        """Create the main window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(self.title)
        self.window.geometry(f"{self.width}x{self.height}")
        self.window.configure(bg='#ecf0f1')
        
        # Center the window
        self.center_window()
        
        # Create custom title bar
        self.create_title_bar()
        
        # Create main content area
        self.content_frame = tk.Frame(self.window, bg='white')
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=1, pady=(0, 1))
        
    def create_title_bar(self):
        """Create custom title bar"""
        self.title_bar = tk.Frame(self.window, bg='#3498db', height=30)
        self.title_bar.pack(fill=tk.X)
        self.title_bar.pack_propagate(False)
        
        # Window title
        self.title_label = tk.Label(
            self.title_bar,
            text=self.title,
            font=("Arial", 10, "bold"),
            bg='#3498db',
            fg='white'
        )
        self.title_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # Window controls frame
        controls_frame = tk.Frame(self.title_bar, bg='#3498db')
        controls_frame.pack(side=tk.RIGHT, pady=2, padx=5)
        
        # Minimize button
        self.minimize_btn = tk.Button(
            controls_frame,
            text="−",
            font=("Arial", 12, "bold"),
            bg='#f39c12',
            fg='white',
            relief=tk.FLAT,
            width=2,
            command=self.minimize_window
        )
        self.minimize_btn.pack(side=tk.LEFT, padx=1)
        
        # Maximize button
        self.maximize_btn = tk.Button(
            controls_frame,
            text="□",
            font=("Arial", 10, "bold"),
            bg='#27ae60',
            fg='white',
            relief=tk.FLAT,
            width=2,
            command=self.toggle_maximize
        )
        self.maximize_btn.pack(side=tk.LEFT, padx=1)
        
        # Close button
        self.close_btn = tk.Button(
            controls_frame,
            text="×",
            font=("Arial", 12, "bold"),
            bg='#e74c3c',
            fg='white',
            relief=tk.FLAT,
            width=2,
            command=self.close_window
        )
        self.close_btn.pack(side=tk.LEFT, padx=1)
        
        # Make title bar draggable
        self.title_bar.bind("<Button-1>", self.start_drag)
        self.title_bar.bind("<B1-Motion>", self.drag_window)
        self.title_label.bind("<Button-1>", self.start_drag)
        self.title_label.bind("<B1-Motion>", self.drag_window)
        
    def setup_window_controls(self):
        """Setup window control behaviors"""
        self.is_maximized = False
        self.normal_geometry = None
        
        # Bind window events
        self.window.protocol("WM_DELETE_WINDOW", self.close_window)
        
    def center_window(self):
        """Center the window on screen"""
        self.window.update_idletasks()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        
        x = (screen_width - self.width) // 2
        y = (screen_height - self.height) // 2
        
        self.window.geometry(f"{self.width}x{self.height}+{x}+{y}")
        
    def start_drag(self, event):
        """Start dragging the window"""
        self.drag_start_x = event.x_root
        self.drag_start_y = event.y_root
        
    def drag_window(self, event):
        """Drag the window"""
        if not self.is_maximized:
            x = self.window.winfo_x() + (event.x_root - self.drag_start_x)
            y = self.window.winfo_y() + (event.y_root - self.drag_start_y)
            self.window.geometry(f"+{x}+{y}")
            self.drag_start_x = event.x_root
            self.drag_start_y = event.y_root
            
    def minimize_window(self):
        """Minimize the window"""
        self.window.iconify()
        
    def toggle_maximize(self):
        """Toggle window maximize state"""
        if self.is_maximized:
            # Restore to normal size
            if self.normal_geometry:
                self.window.geometry(self.normal_geometry)
            self.is_maximized = False
            self.maximize_btn.config(text="□")
        else:
            # Maximize window
            self.normal_geometry = self.window.geometry()
            self.window.state('zoomed')  # Windows maximize
            self.is_maximized = True
            self.maximize_btn.config(text="❐")
            
    def close_window(self):
        """Close the window"""
        self.on_close()
        self.window.destroy()
        
    def on_close(self):
        """Override this method for custom close behavior"""
        pass
        
    def set_title(self, title):
        """Set window title"""
        self.title = title
        self.window.title(title)
        self.title_label.config(text=title)
        
    def get_content_frame(self):
        """Get the main content frame for adding widgets"""
        return self.content_frame
        
    def show(self):
        """Show the window"""
        self.window.deiconify()
        self.window.lift()
        self.window.focus_set()
        
    def hide(self):
        """Hide the window"""
        self.window.withdraw()
        
    def get_window(self):
        """Get the underlying tkinter window"""
        return self.window
