"""
Taskbar Component - Bottom taskbar with app launcher, running apps, and system tray
"""

import tkinter as tk
from tkinter import ttk
import psutil
import threading
import time

class Taskbar:
    def __init__(self, root, app_launcher):
        self.root = root
        self.app_launcher = app_launcher
        self.running_apps = {}
        
        self.create_taskbar()
        self.start_system_monitor()
        
    def create_taskbar(self):
        """Create the taskbar UI"""
        # Main taskbar frame
        self.taskbar_frame = tk.Frame(self.root, bg='#34495e', height=40)
        self.taskbar_frame.pack(side=tk.BOTTOM, fill=tk.X)
        self.taskbar_frame.pack_propagate(False)
        
        # Left side - App launcher and running apps
        self.left_frame = tk.Frame(self.taskbar_frame, bg='#34495e')
        self.left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # App launcher button (Start menu equivalent)
        self.start_btn = tk.Button(
            self.left_frame,
            text="🐍 Python OS",
            font=("Arial", 10, "bold"),
            bg='#3498db',
            fg='white',
            relief=tk.FLAT,
            padx=10,
            command=self.show_start_menu
        )
        self.start_btn.pack(side=tk.LEFT, pady=5)
        
        # Running apps frame
        self.apps_frame = tk.Frame(self.left_frame, bg='#34495e')
        self.apps_frame.pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # Right side - System tray
        self.right_frame = tk.Frame(self.taskbar_frame, bg='#34495e')
        self.right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=5)
        
        # System info
        self.system_info_label = tk.Label(
            self.right_frame,
            text="CPU: 0% | RAM: 0%",
            font=("Arial", 8),
            bg='#34495e',
            fg='white'
        )
        self.system_info_label.pack(side=tk.RIGHT, pady=5, padx=5)
        
        # Clock
        self.clock_label = tk.Label(
            self.right_frame,
            text="00:00:00",
            font=("Arial", 10, "bold"),
            bg='#34495e',
            fg='white'
        )
        self.clock_label.pack(side=tk.RIGHT, pady=5, padx=10)
        
        # Date
        self.date_label = tk.Label(
            self.right_frame,
            text="0000-00-00",
            font=("Arial", 8),
            bg='#34495e',
            fg='white'
        )
        self.date_label.pack(side=tk.RIGHT, pady=5, padx=5)
        
        # Create start menu
        self.create_start_menu()
        
    def create_start_menu(self):
        """Create the start menu"""
        self.start_menu = tk.Menu(self.root, tearoff=0)
        
        # Applications submenu
        apps_menu = tk.Menu(self.start_menu, tearoff=0)
        apps_menu.add_command(label="File Manager", command=lambda: self.app_launcher.launch_app('file_manager'))
        apps_menu.add_command(label="Web Browser", command=lambda: self.app_launcher.launch_app('browser'))
        apps_menu.add_command(label="Terminal", command=lambda: self.app_launcher.launch_app('terminal'))
        apps_menu.add_command(label="Text Editor", command=lambda: self.app_launcher.launch_app('text_editor'))
        
        self.start_menu.add_cascade(label="Applications", menu=apps_menu)
        self.start_menu.add_separator()
        self.start_menu.add_command(label="Settings", command=lambda: self.app_launcher.launch_app('settings'))
        self.start_menu.add_command(label="System Info", command=self.show_system_info)
        self.start_menu.add_separator()
        self.start_menu.add_command(label="Restart", command=self.restart_os)
        self.start_menu.add_command(label="Shutdown", command=self.shutdown_os)
        
    def show_start_menu(self):
        """Show the start menu"""
        try:
            x = self.start_btn.winfo_rootx()
            y = self.start_btn.winfo_rooty() - 200
            self.start_menu.tk_popup(x, y)
        finally:
            self.start_menu.grab_release()
            
    def add_running_app(self, app_name, window_ref):
        """Add a running app to the taskbar"""
        if app_name not in self.running_apps:
            app_btn = tk.Button(
                self.apps_frame,
                text=app_name,
                font=("Arial", 9),
                bg='#2c3e50',
                fg='white',
                relief=tk.FLAT,
                padx=8,
                pady=2,
                command=lambda: self.focus_app(window_ref)
            )
            app_btn.pack(side=tk.LEFT, padx=2)
            
            self.running_apps[app_name] = {
                'button': app_btn,
                'window': window_ref
            }
            
    def remove_running_app(self, app_name):
        """Remove a running app from the taskbar"""
        if app_name in self.running_apps:
            self.running_apps[app_name]['button'].destroy()
            del self.running_apps[app_name]
            
    def focus_app(self, window_ref):
        """Focus on a running application"""
        try:
            window_ref.lift()
            window_ref.focus_set()
        except:
            pass
            
    def update_clock(self, time_str, date_str):
        """Update the clock display"""
        try:
            self.clock_label.config(text=time_str)
            self.date_label.config(text=date_str)
        except:
            pass
            
    def start_system_monitor(self):
        """Start system monitoring thread"""
        def monitor_system():
            while True:
                try:
                    cpu_percent = psutil.cpu_percent(interval=1)
                    memory = psutil.virtual_memory()
                    ram_percent = memory.percent
                    
                    info_text = f"CPU: {cpu_percent:.1f}% | RAM: {ram_percent:.1f}%"
                    self.system_info_label.config(text=info_text)
                    
                    time.sleep(2)
                except:
                    break
                    
        monitor_thread = threading.Thread(target=monitor_system, daemon=True)
        monitor_thread.start()
        
    def show_system_info(self):
        """Show detailed system information"""
        try:
            import platform
            from tkinter import messagebox
            
            # Get system information
            system_info = f"""System Information:

OS: {platform.system()} {platform.release()}
Architecture: {platform.architecture()[0]}
Processor: {platform.processor()}
Python Version: {platform.python_version()}

CPU Usage: {psutil.cpu_percent()}%
Memory Usage: {psutil.virtual_memory().percent}%
Available Memory: {psutil.virtual_memory().available / (1024**3):.1f} GB
Total Memory: {psutil.virtual_memory().total / (1024**3):.1f} GB

Disk Usage: {psutil.disk_usage('/').percent}%"""
            
            messagebox.showinfo("System Information", system_info)
        except Exception as e:
            messagebox.showerror("Error", f"Could not retrieve system info: {e}")
            
    def restart_os(self):
        """Restart the OS"""
        from tkinter import messagebox
        if messagebox.askyesno("Restart", "Are you sure you want to restart Python OS?"):
            self.root.quit()
            # In a real implementation, you might restart the application
            
    def shutdown_os(self):
        """Shutdown the OS"""
        from tkinter import messagebox
        if messagebox.askyesno("Shutdown", "Are you sure you want to shutdown Python OS?"):
            self.root.quit()
