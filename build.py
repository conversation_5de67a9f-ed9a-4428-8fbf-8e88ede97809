#!/usr/bin/env python3
"""
Build script for Python OS - Creates executable using PyInstaller
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = ['pyinstaller', 'psutil', 'pillow', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"Missing required packages: {', '.join(missing_packages)}")
        print("Please install them using: pip install " + " ".join(missing_packages))
        return False
    
    return True

def clean_build():
    """Clean previous build artifacts"""
    print("Cleaning previous build artifacts...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"Removed {dir_name}/")
    
    # Clean __pycache__ directories recursively
    for root, dirs, files in os.walk('.'):
        for dir_name in dirs:
            if dir_name == '__pycache__':
                shutil.rmtree(os.path.join(root, dir_name))
                print(f"Removed {os.path.join(root, dir_name)}")

def create_executable():
    """Create executable using PyInstaller"""
    print("Creating executable with PyInstaller...")
    
    # PyInstaller command
    cmd = [
        'pyinstaller',
        '--onefile',                    # Create single executable file
        '--windowed',                   # Hide console window (Windows)
        '--name=PythonOS',             # Executable name
        '--icon=resources/icons/os_icon.ico',  # Icon (if exists)
        '--add-data=config;config',     # Include config directory
        '--add-data=resources;resources',  # Include resources directory
        '--add-data=ui;ui',             # Include UI modules
        '--add-data=system;system',     # Include system modules
        '--add-data=apps;apps',         # Include apps modules
        '--hidden-import=tkinter',      # Ensure tkinter is included
        '--hidden-import=psutil',       # Ensure psutil is included
        '--hidden-import=PIL',          # Ensure Pillow is included
        '--hidden-import=requests',     # Ensure requests is included
        'main.py'                       # Main script
    ]
    
    # Create icon file if it doesn't exist
    os.makedirs('resources/icons', exist_ok=True)
    icon_path = 'resources/icons/os_icon.ico'
    if not os.path.exists(icon_path):
        print(f"Warning: Icon file {icon_path} not found. Creating placeholder...")
        # Create a simple text file as placeholder
        with open(icon_path.replace('.ico', '.txt'), 'w') as f:
            f.write("Icon placeholder - replace with actual .ico file")
        # Remove icon parameter if file doesn't exist
        cmd = [arg for arg in cmd if not arg.startswith('--icon=')]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("PyInstaller completed successfully!")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"PyInstaller failed with error: {e}")
        print(f"Error output: {e.stderr}")
        return False

def create_installer():
    """Create installer package (optional)"""
    print("Creating installer package...")
    
    # This is a placeholder for creating an installer
    # You could use tools like Inno Setup (Windows) or create a simple zip
    
    if os.path.exists('dist/PythonOS.exe'):
        # Create a simple distribution package
        dist_dir = 'dist/PythonOS_Package'
        os.makedirs(dist_dir, exist_ok=True)
        
        # Copy executable
        shutil.copy2('dist/PythonOS.exe', dist_dir)
        
        # Create README
        readme_content = """Python OS - Installation Instructions

1. Extract all files to a directory of your choice
2. Run PythonOS.exe to start the operating system interface
3. Use the following keyboard shortcuts:
   - Ctrl+Alt+T: Open Terminal
   - Ctrl+Alt+F: Open File Manager
   - Ctrl+Alt+B: Open Browser
   - Alt+F4: Exit

System Requirements:
- Windows 7 or later
- 4GB RAM minimum
- 100MB free disk space

For support and updates, visit: https://github.com/your-repo/python-os
"""
        
        with open(os.path.join(dist_dir, 'README.txt'), 'w') as f:
            f.write(readme_content)
        
        # Create batch file for easy launching
        batch_content = """@echo off
echo Starting Python OS...
PythonOS.exe
pause
"""
        
        with open(os.path.join(dist_dir, 'Start_PythonOS.bat'), 'w') as f:
            f.write(batch_content)
        
        print(f"Distribution package created in: {dist_dir}")
        return True
    
    return False

def main():
    """Main build function"""
    print("=" * 50)
    print("Python OS Build Script")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('main.py'):
        print("Error: main.py not found. Please run this script from the Python OS root directory.")
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Clean previous builds
    clean_build()
    
    # Create executable
    if not create_executable():
        print("Build failed!")
        sys.exit(1)
    
    # Create installer package
    create_installer()
    
    print("\n" + "=" * 50)
    print("Build completed successfully!")
    print("=" * 50)
    
    if os.path.exists('dist/PythonOS.exe'):
        exe_size = os.path.getsize('dist/PythonOS.exe') / (1024 * 1024)
        print(f"Executable created: dist/PythonOS.exe ({exe_size:.1f} MB)")
        print("\nTo run Python OS:")
        print("1. Navigate to the dist/ directory")
        print("2. Double-click PythonOS.exe")
        print("3. Or run from command line: dist/PythonOS.exe")
    
    print("\nBuild artifacts:")
    print("- dist/PythonOS.exe - Main executable")
    print("- dist/PythonOS_Package/ - Distribution package")
    print("- build/ - Build cache (can be deleted)")

if __name__ == "__main__":
    main()
