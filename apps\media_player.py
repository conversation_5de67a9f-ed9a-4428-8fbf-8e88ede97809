"""
Media Player Application - Ultimate audio player with advanced features
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, colorchooser
import os
import threading
import time
import json
import random
import sqlite3
import urllib.request
import urllib.parse
import re
import math
from datetime import timedelta, datetime
from collections import defaultdict

from ui.window import CustomWindow

try:
    import pygame
    pygame.mixer.init(frequency=44100, size=-16, channels=2, buffer=1024)
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False

try:
    import mutagen
    from mutagen.id3 import ID3, TIT2, TPE1, TALB, TDRC, TCON
    MUTAGEN_AVAILABLE = True
except ImportError:
    MUTAGEN_AVAILABLE = False

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False

class MediaPlayer:
    def __init__(self, parent, window_manager, window_id):
        self.parent = parent
        self.window_manager = window_manager
        self.window_id = window_id

        # Basic playback state
        self.current_file = None
        self.playlist = []
        self.current_index = 0
        self.is_playing = False
        self.is_paused = False
        self.position = 0
        self.duration = 0
        self.volume = 70

        # Advanced features
        self.playlists = {"Default": []}  # Multiple playlists
        self.current_playlist_name = "Default"
        self.music_library = {}  # Artist -> Album -> Songs
        self.recently_played = []
        self.favorites = []
        self.play_history = []
        self.radio_stations = []

        # Audio settings
        self.equalizer_bands = [0] * 10  # 10-band equalizer
        self.audio_effects = {
            'reverb': False,
            'echo': False,
            'bass_boost': False,
            'treble_boost': False
        }
        self.crossfade_duration = 3  # seconds
        self.gapless_playback = True
        self.replay_gain = True

        # Visualizer settings
        self.visualizer_type = "bars"  # bars, spectrum, waveform, circle
        self.visualizer_colors = ["#3498db", "#e74c3c", "#f39c12", "#27ae60"]
        self.visualizer_sensitivity = 50

        # UI settings
        self.theme = "dark"
        self.show_album_art = True
        self.show_lyrics = True
        self.mini_mode = False

        # Advanced playback
        self.sleep_timer = None
        self.alarm_time = None
        self.playback_speed = 1.0
        self.pitch_shift = 0

        # Internet features
        self.auto_fetch_lyrics = True
        self.auto_fetch_album_art = True
        self.scrobble_enabled = False

        # Initialize database
        self.init_database()

        # Load settings
        self.load_settings()

        self.create_window()
        self.create_interface()

        if not PYGAME_AVAILABLE:
            self.show_pygame_warning()

        # Start background services
        self.start_background_services()

    def init_database(self):
        """Initialize SQLite database for music library"""
        try:
            os.makedirs('data', exist_ok=True)
            self.db_path = 'data/music_library.db'

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Create tables
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS songs (
                    id INTEGER PRIMARY KEY,
                    filepath TEXT UNIQUE,
                    title TEXT,
                    artist TEXT,
                    album TEXT,
                    genre TEXT,
                    year INTEGER,
                    duration INTEGER,
                    play_count INTEGER DEFAULT 0,
                    last_played TIMESTAMP,
                    rating INTEGER DEFAULT 0,
                    added_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS playlists (
                    id INTEGER PRIMARY KEY,
                    name TEXT UNIQUE,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS playlist_songs (
                    playlist_id INTEGER,
                    song_id INTEGER,
                    position INTEGER,
                    FOREIGN KEY (playlist_id) REFERENCES playlists (id),
                    FOREIGN KEY (song_id) REFERENCES songs (id)
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS radio_stations (
                    id INTEGER PRIMARY KEY,
                    name TEXT,
                    url TEXT,
                    genre TEXT,
                    country TEXT
                )
            ''')

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"Database initialization error: {e}")

    def load_settings(self):
        """Load user settings from file"""
        try:
            settings_file = 'data/media_player_settings.json'
            if os.path.exists(settings_file):
                with open(settings_file, 'r') as f:
                    settings = json.load(f)

                self.volume = settings.get('volume', 70)
                self.theme = settings.get('theme', 'dark')
                self.equalizer_bands = settings.get('equalizer_bands', [0] * 10)
                self.audio_effects = settings.get('audio_effects', self.audio_effects)
                self.visualizer_type = settings.get('visualizer_type', 'bars')
                self.visualizer_colors = settings.get('visualizer_colors', self.visualizer_colors)
                self.crossfade_duration = settings.get('crossfade_duration', 3)
                self.gapless_playback = settings.get('gapless_playback', True)
                self.auto_fetch_lyrics = settings.get('auto_fetch_lyrics', True)
                self.auto_fetch_album_art = settings.get('auto_fetch_album_art', True)

        except Exception as e:
            print(f"Settings loading error: {e}")

    def save_settings(self):
        """Save user settings to file"""
        try:
            os.makedirs('data', exist_ok=True)
            settings = {
                'volume': self.volume,
                'theme': self.theme,
                'equalizer_bands': self.equalizer_bands,
                'audio_effects': self.audio_effects,
                'visualizer_type': self.visualizer_type,
                'visualizer_colors': self.visualizer_colors,
                'crossfade_duration': self.crossfade_duration,
                'gapless_playback': self.gapless_playback,
                'auto_fetch_lyrics': self.auto_fetch_lyrics,
                'auto_fetch_album_art': self.auto_fetch_album_art
            }

            with open('data/media_player_settings.json', 'w') as f:
                json.dump(settings, f, indent=4)

        except Exception as e:
            print(f"Settings saving error: {e}")

    def start_background_services(self):
        """Start background services"""
        # Start library scanner
        threading.Thread(target=self.scan_music_library, daemon=True).start()

        # Start radio station loader
        threading.Thread(target=self.load_radio_stations, daemon=True).start()

    def create_window(self):
        """Create the media player window"""
        self.custom_window = CustomWindow(
            self.parent,
            title="Ultimate Media Player Pro",
            width=1200,
            height=800
        )
        self.window = self.custom_window.get_window()
        self.content_frame = self.custom_window.get_content_frame()

        # Apply theme
        self.apply_theme()

    def create_interface(self):
        """Create the advanced media player interface"""
        # Menu bar
        self.create_enhanced_menu()

        # Create main notebook for tabs
        self.main_notebook = ttk.Notebook(self.content_frame)
        self.main_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create tabs
        self.create_player_tab()
        self.create_library_tab()
        self.create_radio_tab()
        self.create_equalizer_tab()
        self.create_visualizer_tab()
        self.create_settings_tab()

        # Create bottom status bar
        self.create_status_bar()

    def create_enhanced_menu(self):
        """Create enhanced menu bar with more options"""
        menubar = tk.Menu(self.window)
        self.window.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Open File(s)...", command=self.open_file, accelerator="Ctrl+O")
        file_menu.add_command(label="Open Folder...", command=self.open_folder, accelerator="Ctrl+Shift+O")
        file_menu.add_command(label="Open URL...", command=self.open_url)
        file_menu.add_separator()
        file_menu.add_command(label="Import Playlist...", command=self.load_playlist)
        file_menu.add_command(label="Export Playlist...", command=self.save_playlist)
        file_menu.add_separator()
        file_menu.add_command(label="Scan Music Library", command=self.scan_music_library)
        file_menu.add_command(label="Clear Library", command=self.clear_library)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.window.destroy, accelerator="Alt+F4")

        # Edit menu
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Select All", command=self.select_all_playlist, accelerator="Ctrl+A")
        edit_menu.add_command(label="Clear Playlist", command=self.clear_playlist, accelerator="Ctrl+L")
        edit_menu.add_command(label="Remove Duplicates", command=self.remove_duplicates)
        edit_menu.add_separator()
        edit_menu.add_command(label="Edit Track Info...", command=self.edit_track_info)
        edit_menu.add_command(label="Preferences...", command=self.show_preferences, accelerator="Ctrl+P")

        # Playback menu
        playback_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Playback", menu=playback_menu)
        playback_menu.add_command(label="Play/Pause", command=self.toggle_playback, accelerator="Space")
        playback_menu.add_command(label="Stop", command=self.stop, accelerator="Ctrl+.")
        playback_menu.add_command(label="Previous Track", command=self.previous_track, accelerator="Ctrl+Left")
        playback_menu.add_command(label="Next Track", command=self.next_track, accelerator="Ctrl+Right")
        playback_menu.add_separator()
        playback_menu.add_command(label="Shuffle", command=self.toggle_shuffle, accelerator="Ctrl+S")
        playback_menu.add_command(label="Repeat", command=self.toggle_repeat, accelerator="Ctrl+R")
        playback_menu.add_separator()
        playback_menu.add_command(label="Increase Volume", command=self.volume_up, accelerator="Ctrl+Up")
        playback_menu.add_command(label="Decrease Volume", command=self.volume_down, accelerator="Ctrl+Down")
        playback_menu.add_command(label="Mute", command=self.toggle_mute, accelerator="Ctrl+M")

        # Audio menu
        audio_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Audio", menu=audio_menu)
        audio_menu.add_command(label="Equalizer...", command=self.show_equalizer)
        audio_menu.add_command(label="Audio Effects...", command=self.show_audio_effects)
        audio_menu.add_separator()
        audio_menu.add_command(label="Increase Speed", command=self.speed_up)
        audio_menu.add_command(label="Decrease Speed", command=self.speed_down)
        audio_menu.add_command(label="Reset Speed", command=self.reset_speed)
        audio_menu.add_separator()
        audio_menu.add_command(label="Pitch Up", command=self.pitch_up)
        audio_menu.add_command(label="Pitch Down", command=self.pitch_down)
        audio_menu.add_command(label="Reset Pitch", command=self.reset_pitch)

        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Mini Player", command=self.toggle_mini_mode, accelerator="Ctrl+1")
        view_menu.add_command(label="Full Screen", command=self.toggle_fullscreen, accelerator="F11")
        view_menu.add_separator()
        view_menu.add_command(label="Show Visualizer", command=self.toggle_visualizer)
        view_menu.add_command(label="Show Lyrics", command=self.toggle_lyrics)
        view_menu.add_command(label="Show Album Art", command=self.toggle_album_art)
        view_menu.add_separator()

        # Theme submenu
        theme_menu = tk.Menu(view_menu, tearoff=0)
        view_menu.add_cascade(label="Theme", menu=theme_menu)
        theme_menu.add_command(label="Dark", command=lambda: self.set_theme("dark"))
        theme_menu.add_command(label="Light", command=lambda: self.set_theme("light"))
        theme_menu.add_command(label="Blue", command=lambda: self.set_theme("blue"))
        theme_menu.add_command(label="Green", command=lambda: self.set_theme("green"))
        theme_menu.add_command(label="Custom...", command=self.custom_theme)

        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Sleep Timer...", command=self.set_sleep_timer)
        tools_menu.add_command(label="Alarm Clock...", command=self.set_alarm)
        tools_menu.add_separator()
        tools_menu.add_command(label="Convert Audio...", command=self.convert_audio)
        tools_menu.add_command(label="Download Lyrics", command=self.download_lyrics)
        tools_menu.add_command(label="Download Album Art", command=self.download_album_art)
        tools_menu.add_separator()
        tools_menu.add_command(label="Statistics...", command=self.show_statistics)
        tools_menu.add_command(label="Export Play History", command=self.export_history)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="Keyboard Shortcuts", command=self.show_shortcuts)
        help_menu.add_command(label="About Codecs", command=self.show_codec_info)
        help_menu.add_separator()
        help_menu.add_command(label="Check for Updates", command=self.check_updates)
        help_menu.add_command(label="About", command=self.show_about)

    def create_player_tab(self):
        """Create the main player tab"""
        player_frame = ttk.Frame(self.main_notebook)
        self.main_notebook.add(player_frame, text="🎵 Player")

        # Create main layout
        main_paned = ttk.PanedWindow(player_frame, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel - Playlists and Queue
        left_panel = ttk.Frame(main_paned)
        main_paned.add(left_panel, weight=1)

        # Playlist tabs
        playlist_notebook = ttk.Notebook(left_panel)
        playlist_notebook.pack(fill=tk.BOTH, expand=True)

        # Current playlist tab
        self.create_playlist_panel(playlist_notebook)

        # Queue tab
        self.create_queue_panel(playlist_notebook)

        # Recently played tab
        self.create_recent_panel(playlist_notebook)

        # Right panel - Player controls and info
        right_panel = ttk.Frame(main_paned)
        main_paned.add(right_panel, weight=2)

        # Create enhanced player panel
        self.create_enhanced_player_panel(right_panel)

    def create_library_tab(self):
        """Create the music library tab"""
        library_frame = ttk.Frame(self.main_notebook)
        self.main_notebook.add(library_frame, text="📚 Library")

        # Create library browser
        library_paned = ttk.PanedWindow(library_frame, orient=tk.HORIZONTAL)
        library_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left - Tree view for artists/albums
        tree_frame = ttk.Frame(library_paned)
        library_paned.add(tree_frame, weight=1)

        tk.Label(tree_frame, text="Music Library", font=("Arial", 14, "bold")).pack(pady=5)

        # Search box
        search_frame = tk.Frame(tree_frame)
        search_frame.pack(fill=tk.X, padx=5, pady=5)

        tk.Label(search_frame, text="Search:").pack(side=tk.LEFT)
        self.library_search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.library_search_var)
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        search_entry.bind('<KeyRelease>', self.filter_library)

        # Library tree
        self.library_tree = ttk.Treeview(tree_frame)
        self.library_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Right - Song list
        songs_frame = ttk.Frame(library_paned)
        library_paned.add(songs_frame, weight=2)

        tk.Label(songs_frame, text="Songs", font=("Arial", 14, "bold")).pack(pady=5)

        # Songs listbox with details
        columns = ('Title', 'Artist', 'Album', 'Duration', 'Rating')
        self.songs_tree = ttk.Treeview(songs_frame, columns=columns, show='headings')

        for col in columns:
            self.songs_tree.heading(col, text=col)
            self.songs_tree.column(col, width=120)

        self.songs_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_radio_tab(self):
        """Create the internet radio tab"""
        radio_frame = ttk.Frame(self.main_notebook)
        self.main_notebook.add(radio_frame, text="📻 Radio")

        # Radio controls
        controls_frame = tk.Frame(radio_frame)
        controls_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(controls_frame, text="Internet Radio Stations", font=("Arial", 14, "bold")).pack(side=tk.LEFT)

        tk.Button(controls_frame, text="Add Station", command=self.add_radio_station).pack(side=tk.RIGHT, padx=5)
        tk.Button(controls_frame, text="Refresh", command=self.refresh_radio_stations).pack(side=tk.RIGHT, padx=5)

        # Radio stations list
        radio_list_frame = tk.Frame(radio_frame)
        radio_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        columns = ('Station', 'Genre', 'Country', 'Bitrate')
        self.radio_tree = ttk.Treeview(radio_list_frame, columns=columns, show='headings')

        for col in columns:
            self.radio_tree.heading(col, text=col)
            self.radio_tree.column(col, width=150)

        self.radio_tree.pack(fill=tk.BOTH, expand=True)

        # Bind double-click to play
        self.radio_tree.bind('<Double-1>', self.play_radio_station)

    def create_equalizer_tab(self):
        """Create the equalizer tab"""
        eq_frame = ttk.Frame(self.main_notebook)
        self.main_notebook.add(eq_frame, text="🎛️ Equalizer")

        # Equalizer controls
        eq_controls = tk.Frame(eq_frame)
        eq_controls.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(eq_controls, text="10-Band Equalizer", font=("Arial", 16, "bold")).pack()

        # Preset selection
        preset_frame = tk.Frame(eq_controls)
        preset_frame.pack(fill=tk.X, pady=10)

        tk.Label(preset_frame, text="Preset:").pack(side=tk.LEFT)
        self.eq_preset_var = tk.StringVar(value="Flat")
        preset_combo = ttk.Combobox(preset_frame, textvariable=self.eq_preset_var,
                                   values=["Flat", "Rock", "Pop", "Jazz", "Classical", "Electronic", "Hip-Hop", "Custom"])
        preset_combo.pack(side=tk.LEFT, padx=5)
        preset_combo.bind('<<ComboboxSelected>>', self.apply_eq_preset)

        # Equalizer sliders
        eq_sliders_frame = tk.Frame(eq_frame)
        eq_sliders_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        self.eq_sliders = []
        eq_frequencies = ["32Hz", "64Hz", "125Hz", "250Hz", "500Hz", "1kHz", "2kHz", "4kHz", "8kHz", "16kHz"]

        for i, freq in enumerate(eq_frequencies):
            slider_frame = tk.Frame(eq_sliders_frame)
            slider_frame.pack(side=tk.LEFT, fill=tk.Y, expand=True, padx=5)

            tk.Label(slider_frame, text=freq, font=("Arial", 10)).pack()

            slider = tk.Scale(slider_frame, from_=12, to=-12, orient=tk.VERTICAL,
                            length=200, resolution=0.5, command=lambda v, i=i: self.update_eq_band(i, v))
            slider.set(self.equalizer_bands[i])
            slider.pack()

            tk.Label(slider_frame, text="dB", font=("Arial", 8)).pack()

            self.eq_sliders.append(slider)

    def create_visualizer_tab(self):
        """Create the visualizer tab"""
        viz_frame = ttk.Frame(self.main_notebook)
        self.main_notebook.add(viz_frame, text="🌈 Visualizer")

        # Visualizer controls
        viz_controls = tk.Frame(viz_frame)
        viz_controls.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(viz_controls, text="Audio Visualizer", font=("Arial", 16, "bold")).pack(side=tk.LEFT)

        # Visualizer type selection
        type_frame = tk.Frame(viz_controls)
        type_frame.pack(side=tk.RIGHT)

        tk.Label(type_frame, text="Type:").pack(side=tk.LEFT)
        self.viz_type_var = tk.StringVar(value=self.visualizer_type)
        type_combo = ttk.Combobox(type_frame, textvariable=self.viz_type_var,
                                 values=["bars", "spectrum", "waveform", "circle", "spiral", "3d_bars"])
        type_combo.pack(side=tk.LEFT, padx=5)
        type_combo.bind('<<ComboboxSelected>>', self.change_visualizer_type)

        # Visualizer canvas
        self.main_visualizer_canvas = tk.Canvas(viz_frame, bg='black', height=400)
        self.main_visualizer_canvas.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Visualizer settings
        settings_frame = tk.Frame(viz_frame)
        settings_frame.pack(fill=tk.X, padx=10, pady=5)

        # Sensitivity
        tk.Label(settings_frame, text="Sensitivity:").pack(side=tk.LEFT)
        self.viz_sensitivity_var = tk.IntVar(value=self.visualizer_sensitivity)
        sensitivity_scale = tk.Scale(settings_frame, from_=1, to=100, orient=tk.HORIZONTAL,
                                   variable=self.viz_sensitivity_var, command=self.update_viz_sensitivity)
        sensitivity_scale.pack(side=tk.LEFT, padx=5)

        # Color selection
        tk.Button(settings_frame, text="Colors...", command=self.choose_viz_colors).pack(side=tk.RIGHT, padx=5)

    def create_settings_tab(self):
        """Create the settings tab"""
        settings_frame = ttk.Frame(self.main_notebook)
        self.main_notebook.add(settings_frame, text="⚙️ Settings")

        # Create settings notebook
        settings_notebook = ttk.Notebook(settings_frame)
        settings_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Audio settings
        audio_settings = ttk.Frame(settings_notebook)
        settings_notebook.add(audio_settings, text="Audio")

        # Playback settings
        playback_settings = ttk.Frame(settings_notebook)
        settings_notebook.add(playback_settings, text="Playback")

        # Interface settings
        interface_settings = ttk.Frame(settings_notebook)
        settings_notebook.add(interface_settings, text="Interface")

        # Internet settings
        internet_settings = ttk.Frame(settings_notebook)
        settings_notebook.add(internet_settings, text="Internet")

        self.create_audio_settings(audio_settings)
        self.create_playback_settings(playback_settings)
        self.create_interface_settings(interface_settings)
        self.create_internet_settings(internet_settings)

    def create_status_bar(self):
        """Create status bar at bottom"""
        self.status_bar = tk.Frame(self.content_frame, relief=tk.SUNKEN, bd=1)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Status labels
        self.status_label = tk.Label(self.status_bar, text="Ready", anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, padx=5)

        self.codec_label = tk.Label(self.status_bar, text="", anchor=tk.W)
        self.codec_label.pack(side=tk.LEFT, padx=20)

        self.bitrate_label = tk.Label(self.status_bar, text="", anchor=tk.W)
        self.bitrate_label.pack(side=tk.LEFT, padx=20)

        self.time_remaining_label = tk.Label(self.status_bar, text="", anchor=tk.E)
        self.time_remaining_label.pack(side=tk.RIGHT, padx=5)

    def apply_theme(self):
        """Apply the current theme to the interface"""
        themes = {
            'dark': {'bg': '#2c3e50', 'fg': '#ecf0f1', 'select_bg': '#3498db'},
            'light': {'bg': '#ecf0f1', 'fg': '#2c3e50', 'select_bg': '#3498db'},
            'blue': {'bg': '#34495e', 'fg': '#ecf0f1', 'select_bg': '#3498db'},
            'green': {'bg': '#27ae60', 'fg': '#ecf0f1', 'select_bg': '#2ecc71'}
        }

        if self.theme in themes:
            theme_colors = themes[self.theme]
            self.content_frame.configure(bg=theme_colors['bg'])

    def scan_music_library(self):
        """Scan and index music library"""
        try:
            # This would scan common music directories
            music_dirs = [
                os.path.expanduser("~/Music"),
                os.path.expanduser("~/Documents/Music"),
                "C:/Users/<USER>/Music" if os.name == 'nt' else "/home/<USER>"
            ]

            for music_dir in music_dirs:
                if os.path.exists(music_dir):
                    self.scan_directory(music_dir)

        except Exception as e:
            print(f"Library scan error: {e}")

    def scan_directory(self, directory):
        """Scan a directory for music files"""
        try:
            audio_extensions = ['.mp3', '.wav', '.ogg', '.m4a', '.flac', '.aac', '.wma']

            for root, dirs, files in os.walk(directory):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in audio_extensions):
                        filepath = os.path.join(root, file)
                        self.add_to_library(filepath)

        except Exception as e:
            print(f"Directory scan error: {e}")

    def add_to_library(self, filepath):
        """Add a file to the music library database"""
        try:
            if not MUTAGEN_AVAILABLE:
                return

            # Extract metadata
            metadata = self.extract_metadata(filepath)

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO songs
                (filepath, title, artist, album, genre, year, duration)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                filepath,
                metadata.get('title', os.path.basename(filepath)),
                metadata.get('artist', 'Unknown Artist'),
                metadata.get('album', 'Unknown Album'),
                metadata.get('genre', 'Unknown'),
                metadata.get('year', 0),
                metadata.get('duration', 0)
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"Library add error: {e}")

    def extract_metadata(self, filepath):
        """Extract metadata from audio file"""
        metadata = {}
        try:
            if MUTAGEN_AVAILABLE:
                audio_file = mutagen.File(filepath)
                if audio_file:
                    metadata['title'] = str(audio_file.get('TIT2', [''])[0]) if audio_file.get('TIT2') else os.path.basename(filepath)
                    metadata['artist'] = str(audio_file.get('TPE1', ['Unknown Artist'])[0]) if audio_file.get('TPE1') else 'Unknown Artist'
                    metadata['album'] = str(audio_file.get('TALB', ['Unknown Album'])[0]) if audio_file.get('TALB') else 'Unknown Album'
                    metadata['genre'] = str(audio_file.get('TCON', ['Unknown'])[0]) if audio_file.get('TCON') else 'Unknown'
                    metadata['year'] = int(str(audio_file.get('TDRC', [0])[0])[:4]) if audio_file.get('TDRC') else 0
                    metadata['duration'] = int(audio_file.info.length) if hasattr(audio_file, 'info') else 0
        except:
            pass
        return metadata

    def load_radio_stations(self):
        """Load internet radio stations"""
        try:
            # Load from database or default stations
            default_stations = [
                {"name": "BBC Radio 1", "url": "http://bbcmedia.ic.llnwd.net/stream/bbcmedia_radio1_mf_p", "genre": "Pop", "country": "UK"},
                {"name": "Jazz FM", "url": "http://jazz-wr04.ice.infomaniak.ch/jazz-wr04-128.mp3", "genre": "Jazz", "country": "UK"},
                {"name": "Classical FM", "url": "http://media-ice.musicradio.com/ClassicFMMP3", "genre": "Classical", "country": "UK"},
                {"name": "Electronic Radio", "url": "http://stream.electroradio.fm:8000/stream", "genre": "Electronic", "country": "Global"}
            ]

            self.radio_stations = default_stations

        except Exception as e:
            print(f"Radio stations loading error: {e}")

    def create_menu(self):
        """Create menu bar"""
        menubar = tk.Menu(self.window)
        self.window.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Open File", command=self.open_file)
        file_menu.add_command(label="Open Folder", command=self.open_folder)
        file_menu.add_separator()
        file_menu.add_command(label="Save Playlist", command=self.save_playlist)
        file_menu.add_command(label="Load Playlist", command=self.load_playlist)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.window.destroy)

        # Playback menu
        playback_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Playback", menu=playback_menu)
        playback_menu.add_command(label="Play/Pause", command=self.toggle_playback)
        playback_menu.add_command(label="Stop", command=self.stop)
        playback_menu.add_command(label="Previous", command=self.previous_track)
        playback_menu.add_command(label="Next", command=self.next_track)
        playback_menu.add_separator()
        playback_menu.add_command(label="Shuffle", command=self.toggle_shuffle)
        playback_menu.add_command(label="Repeat", command=self.toggle_repeat)

    def create_playlist_panel(self, parent):
        """Create playlist panel"""
        # Playlist header
        playlist_header = tk.Frame(parent)
        playlist_header.pack(fill=tk.X, pady=(0, 5))

        tk.Label(playlist_header, text="Playlist", font=("Arial", 12, "bold")).pack(side=tk.LEFT)

        # Playlist controls
        playlist_controls = tk.Frame(playlist_header)
        playlist_controls.pack(side=tk.RIGHT)

        tk.Button(playlist_controls, text="+", command=self.add_files, width=3).pack(side=tk.LEFT, padx=1)
        tk.Button(playlist_controls, text="-", command=self.remove_selected, width=3).pack(side=tk.LEFT, padx=1)
        tk.Button(playlist_controls, text="↑", command=self.move_up, width=3).pack(side=tk.LEFT, padx=1)
        tk.Button(playlist_controls, text="↓", command=self.move_down, width=3).pack(side=tk.LEFT, padx=1)

        # Playlist listbox
        playlist_frame = tk.Frame(parent)
        playlist_frame.pack(fill=tk.BOTH, expand=True)

        self.playlist_listbox = tk.Listbox(playlist_frame, selectmode=tk.SINGLE)
        playlist_scrollbar = ttk.Scrollbar(playlist_frame, orient=tk.VERTICAL, command=self.playlist_listbox.yview)
        self.playlist_listbox.configure(yscrollcommand=playlist_scrollbar.set)

        self.playlist_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        playlist_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Bind double-click to play
        self.playlist_listbox.bind('<Double-1>', self.play_selected)

        # Playlist info
        self.playlist_info = tk.Label(parent, text="0 tracks", font=("Arial", 9))
        self.playlist_info.pack(pady=(5, 0))

    def create_player_panel(self, parent):
        """Create player control panel"""
        # Now playing info
        info_frame = tk.LabelFrame(parent, text="Now Playing", padx=10, pady=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        self.track_title = tk.Label(info_frame, text="No track selected", font=("Arial", 14, "bold"))
        self.track_title.pack()

        self.track_info = tk.Label(info_frame, text="", font=("Arial", 10))
        self.track_info.pack()

        # Progress bar
        progress_frame = tk.Frame(parent)
        progress_frame.pack(fill=tk.X, pady=(0, 10))

        self.time_label = tk.Label(progress_frame, text="00:00", font=("Arial", 10))
        self.time_label.pack(side=tk.LEFT)

        self.progress_var = tk.DoubleVar()
        self.progress_scale = tk.Scale(
            progress_frame,
            from_=0,
            to=100,
            orient=tk.HORIZONTAL,
            variable=self.progress_var,
            showvalue=0,
            command=self.seek
        )
        self.progress_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=10)

        self.duration_label = tk.Label(progress_frame, text="00:00", font=("Arial", 10))
        self.duration_label.pack(side=tk.RIGHT)

        # Control buttons
        controls_frame = tk.Frame(parent)
        controls_frame.pack(pady=10)

        # Main playback controls
        main_controls = tk.Frame(controls_frame)
        main_controls.pack()

        self.prev_btn = tk.Button(
            main_controls,
            text="⏮",
            command=self.previous_track,
            font=("Arial", 16),
            width=4,
            height=2
        )
        self.prev_btn.pack(side=tk.LEFT, padx=5)

        self.play_pause_btn = tk.Button(
            main_controls,
            text="▶",
            command=self.toggle_playback,
            font=("Arial", 20),
            width=4,
            height=2,
            bg='#27ae60',
            fg='white'
        )
        self.play_pause_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = tk.Button(
            main_controls,
            text="⏹",
            command=self.stop,
            font=("Arial", 16),
            width=4,
            height=2
        )
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        self.next_btn = tk.Button(
            main_controls,
            text="⏭",
            command=self.next_track,
            font=("Arial", 16),
            width=4,
            height=2
        )
        self.next_btn.pack(side=tk.LEFT, padx=5)

        # Secondary controls
        secondary_controls = tk.Frame(controls_frame)
        secondary_controls.pack(pady=(10, 0))

        # Volume control
        volume_frame = tk.Frame(secondary_controls)
        volume_frame.pack(side=tk.LEFT, padx=20)

        tk.Label(volume_frame, text="🔊", font=("Arial", 12)).pack(side=tk.LEFT)

        self.volume_var = tk.IntVar(value=self.volume)
        self.volume_scale = tk.Scale(
            volume_frame,
            from_=0,
            to=100,
            orient=tk.HORIZONTAL,
            variable=self.volume_var,
            command=self.set_volume,
            length=150
        )
        self.volume_scale.pack(side=tk.LEFT, padx=5)

        # Mode buttons
        mode_frame = tk.Frame(secondary_controls)
        mode_frame.pack(side=tk.RIGHT, padx=20)

        self.shuffle_var = tk.BooleanVar()
        self.shuffle_btn = tk.Checkbutton(
            mode_frame,
            text="🔀",
            variable=self.shuffle_var,
            font=("Arial", 12),
            command=self.toggle_shuffle
        )
        self.shuffle_btn.pack(side=tk.LEFT, padx=5)

        self.repeat_var = tk.BooleanVar()
        self.repeat_btn = tk.Checkbutton(
            mode_frame,
            text="🔁",
            variable=self.repeat_var,
            font=("Arial", 12),
            command=self.toggle_repeat
        )
        self.repeat_btn.pack(side=tk.LEFT, padx=5)

        # Visualizer placeholder
        visualizer_frame = tk.LabelFrame(parent, text="Visualizer", padx=10, pady=10)
        visualizer_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        self.visualizer_canvas = tk.Canvas(
            visualizer_frame,
            bg='black',
            height=150
        )
        self.visualizer_canvas.pack(fill=tk.BOTH, expand=True)

        # Add some visual elements
        self.create_visualizer_elements()

    def create_visualizer_elements(self):
        """Create basic visualizer elements"""
        # Simple bars for visualization
        self.visualizer_bars = []
        canvas_width = 400  # Approximate width
        bar_count = 20
        bar_width = canvas_width // bar_count

        for i in range(bar_count):
            x = i * bar_width
            bar = self.visualizer_canvas.create_rectangle(
                x, 150, x + bar_width - 2, 150,
                fill='#3498db',
                outline=''
            )
            self.visualizer_bars.append(bar)

    def show_pygame_warning(self):
        """Show warning if pygame is not available"""
        warning_frame = tk.Frame(self.content_frame, bg='#f39c12')
        warning_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(
            warning_frame,
            text="⚠️ Pygame not installed. Audio playback will be limited.",
            bg='#f39c12',
            fg='white',
            font=("Arial", 10, "bold")
        ).pack(pady=5)

    def open_file(self):
        """Open audio file"""
        filetypes = [
            ("Audio files", "*.mp3 *.wav *.ogg *.m4a"),
            ("MP3 files", "*.mp3"),
            ("WAV files", "*.wav"),
            ("All files", "*.*")
        ]

        filename = filedialog.askopenfilename(
            title="Open Audio File",
            filetypes=filetypes
        )

        if filename:
            self.add_to_playlist(filename)

    def open_folder(self):
        """Open folder and add all audio files"""
        folder = filedialog.askdirectory(title="Select Music Folder")

        if folder:
            audio_extensions = ['.mp3', '.wav', '.ogg', '.m4a', '.flac']
            files_added = 0

            for root, dirs, files in os.walk(folder):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in audio_extensions):
                        filepath = os.path.join(root, file)
                        self.add_to_playlist(filepath)
                        files_added += 1

            messagebox.showinfo("Success", f"Added {files_added} audio files to playlist")

    def add_to_playlist(self, filepath):
        """Add file to playlist"""
        if filepath not in self.playlist:
            self.playlist.append(filepath)
            filename = os.path.basename(filepath)
            self.playlist_listbox.insert(tk.END, filename)
            self.update_playlist_info()

    def add_files(self):
        """Add multiple files to playlist"""
        filetypes = [
            ("Audio files", "*.mp3 *.wav *.ogg *.m4a"),
            ("All files", "*.*")
        ]

        filenames = filedialog.askopenfilenames(
            title="Add Audio Files",
            filetypes=filetypes
        )

        for filename in filenames:
            self.add_to_playlist(filename)

    def remove_selected(self):
        """Remove selected track from playlist"""
        selection = self.playlist_listbox.curselection()
        if selection:
            index = selection[0]
            self.playlist_listbox.delete(index)
            del self.playlist[index]

            # Adjust current index if necessary
            if index <= self.current_index:
                self.current_index = max(0, self.current_index - 1)

            self.update_playlist_info()

    def move_up(self):
        """Move selected track up in playlist"""
        selection = self.playlist_listbox.curselection()
        if selection and selection[0] > 0:
            index = selection[0]

            # Swap in playlist
            self.playlist[index], self.playlist[index-1] = self.playlist[index-1], self.playlist[index]

            # Update listbox
            self.playlist_listbox.delete(index)
            self.playlist_listbox.insert(index-1, os.path.basename(self.playlist[index-1]))
            self.playlist_listbox.selection_set(index-1)

    def move_down(self):
        """Move selected track down in playlist"""
        selection = self.playlist_listbox.curselection()
        if selection and selection[0] < len(self.playlist) - 1:
            index = selection[0]

            # Swap in playlist
            self.playlist[index], self.playlist[index+1] = self.playlist[index+1], self.playlist[index]

            # Update listbox
            self.playlist_listbox.delete(index)
            self.playlist_listbox.insert(index+1, os.path.basename(self.playlist[index+1]))
            self.playlist_listbox.selection_set(index+1)

    def play_selected(self, event=None):
        """Play selected track"""
        selection = self.playlist_listbox.curselection()
        if selection:
            self.current_index = selection[0]
            self.play_current_track()

    def play_current_track(self):
        """Play current track"""
        if not self.playlist or self.current_index >= len(self.playlist):
            return

        self.current_file = self.playlist[self.current_index]
        filename = os.path.basename(self.current_file)

        self.track_title.config(text=filename)
        self.track_info.config(text=f"Track {self.current_index + 1} of {len(self.playlist)}")

        # Highlight current track in playlist
        self.playlist_listbox.selection_clear(0, tk.END)
        self.playlist_listbox.selection_set(self.current_index)
        self.playlist_listbox.see(self.current_index)

        if PYGAME_AVAILABLE:
            try:
                pygame.mixer.music.load(self.current_file)
                pygame.mixer.music.play()
                self.is_playing = True
                self.is_paused = False
                self.play_pause_btn.config(text="⏸", bg='#e74c3c')

                # Start position tracking
                self.start_position_tracking()

            except Exception as e:
                messagebox.showerror("Error", f"Failed to play file: {e}")
        else:
            messagebox.showinfo("Info", f"Would play: {filename}\n(Pygame not available)")

    def toggle_playback(self):
        """Toggle play/pause"""
        if not PYGAME_AVAILABLE:
            messagebox.showinfo("Info", "Pygame not available for audio playback")
            return

        if not self.current_file:
            if self.playlist:
                self.current_index = 0
                self.play_current_track()
            return

        if self.is_playing:
            if self.is_paused:
                pygame.mixer.music.unpause()
                self.is_paused = False
                self.play_pause_btn.config(text="⏸", bg='#e74c3c')
            else:
                pygame.mixer.music.pause()
                self.is_paused = True
                self.play_pause_btn.config(text="▶", bg='#27ae60')
        else:
            self.play_current_track()

    def stop(self):
        """Stop playback"""
        if PYGAME_AVAILABLE:
            pygame.mixer.music.stop()

        self.is_playing = False
        self.is_paused = False
        self.position = 0
        self.play_pause_btn.config(text="▶", bg='#27ae60')
        self.progress_var.set(0)
        self.time_label.config(text="00:00")

    def previous_track(self):
        """Play previous track"""
        if self.playlist:
            self.current_index = (self.current_index - 1) % len(self.playlist)
            self.play_current_track()

    def next_track(self):
        """Play next track"""
        if self.playlist:
            if self.shuffle_var.get():
                import random
                self.current_index = random.randint(0, len(self.playlist) - 1)
            else:
                self.current_index = (self.current_index + 1) % len(self.playlist)
            self.play_current_track()

    def seek(self, value):
        """Seek to position"""
        # Note: pygame doesn't support seeking, this is a placeholder
        pass

    def set_volume(self, value):
        """Set volume"""
        self.volume = int(value)
        if PYGAME_AVAILABLE:
            pygame.mixer.music.set_volume(self.volume / 100.0)

    def toggle_shuffle(self):
        """Toggle shuffle mode"""
        # Shuffle functionality is implemented in next_track
        pass

    def toggle_repeat(self):
        """Toggle repeat mode"""
        # Repeat functionality would be implemented in playback completion
        pass

    def start_position_tracking(self):
        """Start tracking playback position"""
        def track_position():
            while self.is_playing and not self.is_paused:
                if PYGAME_AVAILABLE and pygame.mixer.music.get_busy():
                    # Pygame doesn't provide position info, so we'll simulate it
                    self.position += 1

                    # Update time display
                    time_str = str(timedelta(seconds=self.position))
                    if time_str.startswith('0:'):
                        time_str = time_str[2:]
                    self.time_label.config(text=time_str)

                    # Update visualizer
                    self.update_visualizer()

                    time.sleep(1)
                else:
                    # Track finished
                    if self.repeat_var.get():
                        self.play_current_track()
                    else:
                        self.next_track()
                    break

        if self.is_playing:
            threading.Thread(target=track_position, daemon=True).start()

    def update_visualizer(self):
        """Update visualizer bars"""
        import random

        for bar in self.visualizer_bars:
            # Simulate audio levels with random heights
            height = random.randint(10, 140) if self.is_playing and not self.is_paused else 0

            # Get current coordinates
            coords = self.visualizer_canvas.coords(bar)
            if coords:
                x1, y1, x2, y2 = coords
                # Update bar height
                self.visualizer_canvas.coords(bar, x1, 150 - height, x2, 150)

    def update_playlist_info(self):
        """Update playlist information"""
        count = len(self.playlist)
        self.playlist_info.config(text=f"{count} track{'s' if count != 1 else ''}")

    def save_playlist(self):
        """Save playlist to file"""
        if not self.playlist:
            messagebox.showwarning("Warning", "Playlist is empty")
            return

        filename = filedialog.asksaveasfilename(
            title="Save Playlist",
            defaultextension=".m3u",
            filetypes=[("M3U Playlist", "*.m3u"), ("Text files", "*.txt")]
        )

        if filename:
            try:
                with open(filename, 'w') as f:
                    for track in self.playlist:
                        f.write(f"{track}\n")
                messagebox.showinfo("Success", "Playlist saved successfully")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save playlist: {e}")

    def load_playlist(self):
        """Load playlist from file"""
        filename = filedialog.askopenfilename(
            title="Load Playlist",
            filetypes=[("M3U Playlist", "*.m3u"), ("Text files", "*.txt"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r') as f:
                    tracks = [line.strip() for line in f if line.strip()]

                # Clear current playlist
                self.playlist.clear()
                self.playlist_listbox.delete(0, tk.END)

                # Add tracks
                for track in tracks:
                    if os.path.exists(track):
                        self.add_to_playlist(track)

                messagebox.showinfo("Success", f"Loaded {len(self.playlist)} tracks")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to load playlist: {e}")

    def cleanup(self):
        """Cleanup when closing"""
        if PYGAME_AVAILABLE:
            pygame.mixer.music.stop()
        self.is_playing = False
