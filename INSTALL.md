# Python OS Installation Guide

## Quick Start

### Option 1: Run from Source (Recommended for Development)

1. **Install Python 3.7+** if not already installed
2. **Install dependencies**:
   ```bash
   pip install tkinter psutil pillow requests
   ```
   Or use the requirements file:
   ```bash
   pip install -r requirements.txt
   ```
3. **Run Python OS**:
   ```bash
   python main.py
   ```
   Or on Windows:
   ```bash
   start.bat
   ```

### Option 2: Build Executable

1. **Install build dependencies**:
   ```bash
   pip install pyinstaller psutil pillow requests
   ```
2. **Run the build script**:
   ```bash
   python build.py
   ```
3. **Run the executable**:
   ```bash
   dist/PythonOS.exe
   ```

## Detailed Installation

### Prerequisites

- **Operating System**: Windows 7 or later
- **Python**: 3.7 or higher (for source installation)
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 100MB free space
- **Display**: 1024x768 minimum resolution

### Dependencies

The following Python packages are required:

- `tkinter` - GUI framework (usually included with Python)
- `psutil` - System information and process management
- `pillow` - Image processing (optional, for future enhancements)
- `requests` - HTTP requests for web browser
- `pyinstaller` - For building executable (build only)

### Installation Steps

#### From Source

1. **Download or clone** the Python OS files to a directory
2. **Open command prompt** in the Python OS directory
3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
4. **Test the installation**:
   ```bash
   python test_os.py
   ```
5. **Run Python OS**:
   ```bash
   python main.py
   ```

#### Building Executable

1. **Follow steps 1-4 from source installation**
2. **Install PyInstaller**:
   ```bash
   pip install pyinstaller
   ```
3. **Run build script**:
   ```bash
   python build.py
   ```
4. **Find executable** in `dist/PythonOS.exe`
5. **Optional**: Use the distribution package in `dist/PythonOS_Package/`

### Verification

Run the test suite to verify everything is working:

```bash
python test_os.py
```

You should see:
```
🎉 All tests passed! Python OS is ready to run.
```

### First Run

When you first run Python OS, you'll see:

1. **Desktop Environment** with taskbar and desktop icons
2. **System Information** in the taskbar (CPU/RAM usage)
3. **Clock and Date** in the system tray
4. **Desktop Icons** for applications

### Using Python OS

#### Keyboard Shortcuts

- `Ctrl+Alt+T` - Open Terminal
- `Ctrl+Alt+F` - Open File Manager
- `Ctrl+Alt+B` - Open Web Browser
- `Alt+F4` - Exit Python OS

#### Applications

- **File Manager**: Click the folder icon or press `Ctrl+Alt+F`
- **Web Browser**: Click the globe icon or press `Ctrl+Alt+B`
- **Terminal**: Click the terminal icon or press `Ctrl+Alt+T`
- **Text Editor**: Click the document icon
- **Settings**: Click the gear icon

#### Desktop

- **Right-click** on desktop for context menu
- **Double-click** icons to launch applications
- **Drag** windows to move them
- **Use window controls** to minimize, maximize, or close

### Troubleshooting

#### Common Issues

1. **"tkinter not found"**
   - Install tkinter: `pip install tk`
   - On Ubuntu/Debian: `sudo apt-get install python3-tk`

2. **"psutil not found"**
   - Install psutil: `pip install psutil`

3. **Application won't start**
   - Run test suite: `python test_os.py`
   - Check Python version: `python --version`
   - Verify all files are present

4. **Build fails**
   - Install PyInstaller: `pip install pyinstaller`
   - Check for missing dependencies
   - Run from command prompt with administrator privileges

5. **Performance issues**
   - Close other applications
   - Increase virtual memory
   - Use executable instead of source

#### Getting Help

1. **Run diagnostics**:
   ```bash
   python test_os.py
   ```

2. **Check system requirements**:
   - Python 3.7+
   - 4GB RAM minimum
   - Windows 7+

3. **Verify installation**:
   ```bash
   python -c "import tkinter, psutil; print('Dependencies OK')"
   ```

### Advanced Configuration

#### Customizing Settings

Edit `config/settings.json` to customize:

```json
{
    "theme": "dark",
    "wallpaper": "#2c3e50",
    "taskbar_position": "bottom",
    "auto_hide_taskbar": false,
    "desktop_icons": true,
    "font_size": 12,
    "font_family": "Arial"
}
```

#### Adding Applications

1. Create new Python file in `apps/` directory
2. Implement application class
3. Register in `system/app_launcher.py`

#### Themes

Modify colors and fonts in the settings application or by editing the configuration file directly.

### Uninstallation

#### Source Installation

Simply delete the Python OS directory.

#### Executable Installation

1. Delete the executable file
2. Delete any created shortcuts
3. Remove the Python OS directory

### Support

For issues or questions:

1. Check this installation guide
2. Run the test suite
3. Review the README.md file
4. Check system requirements

---

**Enjoy using Python OS!** 🐍
