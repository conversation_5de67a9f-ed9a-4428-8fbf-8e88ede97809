"""
Window Manager - Manages application windows and their lifecycle
"""

import tkinter as tk
from typing import Dict, List
import threading

class WindowManager:
    def __init__(self, root):
        self.root = root
        self.windows: Dict[str, tk.Toplevel] = {}
        self.window_count = 0
        self.taskbar = None  # Will be set by the desktop
        
    def register_window(self, window_id: str, window: tk.Toplevel, app_name: str = None):
        """Register a new window with the manager"""
        self.windows[window_id] = {
            'window': window,
            'app_name': app_name or window_id,
            'minimized': False,
            'maximized': False
        }
        
        # Add to taskbar if available
        if self.taskbar and app_name:
            self.taskbar.add_running_app(app_name, window)
            
        # Bind window close event
        window.protocol("WM_DELETE_WINDOW", lambda: self.close_window(window_id))
        
        self.window_count += 1
        
    def unregister_window(self, window_id: str):
        """Unregister a window from the manager"""
        if window_id in self.windows:
            window_info = self.windows[window_id]
            
            # Remove from taskbar if available
            if self.taskbar and window_info['app_name']:
                self.taskbar.remove_running_app(window_info['app_name'])
                
            del self.windows[window_id]
            self.window_count -= 1
            
    def close_window(self, window_id: str):
        """Close a specific window"""
        if window_id in self.windows:
            window_info = self.windows[window_id]
            try:
                window_info['window'].destroy()
            except:
                pass
            self.unregister_window(window_id)
            
    def close_all_windows(self):
        """Close all managed windows"""
        window_ids = list(self.windows.keys())
        for window_id in window_ids:
            self.close_window(window_id)
            
    def minimize_window(self, window_id: str):
        """Minimize a window"""
        if window_id in self.windows:
            window_info = self.windows[window_id]
            window_info['window'].iconify()
            window_info['minimized'] = True
            
    def restore_window(self, window_id: str):
        """Restore a minimized window"""
        if window_id in self.windows:
            window_info = self.windows[window_id]
            window_info['window'].deiconify()
            window_info['minimized'] = False
            
    def maximize_window(self, window_id: str):
        """Maximize a window"""
        if window_id in self.windows:
            window_info = self.windows[window_id]
            window_info['window'].state('zoomed')
            window_info['maximized'] = True
            
    def focus_window(self, window_id: str):
        """Focus on a specific window"""
        if window_id in self.windows:
            window_info = self.windows[window_id]
            window = window_info['window']
            
            # Restore if minimized
            if window_info['minimized']:
                self.restore_window(window_id)
                
            # Bring to front and focus
            window.lift()
            window.focus_set()
            
    def get_window_list(self) -> List[Dict]:
        """Get list of all managed windows"""
        return [
            {
                'id': window_id,
                'app_name': info['app_name'],
                'minimized': info['minimized'],
                'maximized': info['maximized']
            }
            for window_id, info in self.windows.items()
        ]
        
    def set_taskbar(self, taskbar):
        """Set the taskbar reference for window management"""
        self.taskbar = taskbar
        
    def tile_windows_horizontal(self):
        """Tile all windows horizontally"""
        if not self.windows:
            return
            
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight() - 40  # Account for taskbar
        
        window_width = screen_width // len(self.windows)
        
        for i, (window_id, window_info) in enumerate(self.windows.items()):
            window = window_info['window']
            x = i * window_width
            y = 0
            
            window.geometry(f"{window_width}x{screen_height}+{x}+{y}")
            window.state('normal')
            window_info['maximized'] = False
            
    def tile_windows_vertical(self):
        """Tile all windows vertically"""
        if not self.windows:
            return
            
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight() - 40  # Account for taskbar
        
        window_height = screen_height // len(self.windows)
        
        for i, (window_id, window_info) in enumerate(self.windows.items()):
            window = window_info['window']
            x = 0
            y = i * window_height
            
            window.geometry(f"{screen_width}x{window_height}+{x}+{y}")
            window.state('normal')
            window_info['maximized'] = False
            
    def cascade_windows(self):
        """Cascade all windows"""
        if not self.windows:
            return
            
        offset = 30
        start_x = 50
        start_y = 50
        
        for i, (window_id, window_info) in enumerate(self.windows.items()):
            window = window_info['window']
            x = start_x + (i * offset)
            y = start_y + (i * offset)
            
            window.geometry(f"800x600+{x}+{y}")
            window.state('normal')
            window_info['maximized'] = False
            
    def minimize_all_windows(self):
        """Minimize all windows"""
        for window_id in self.windows:
            self.minimize_window(window_id)
            
    def restore_all_windows(self):
        """Restore all minimized windows"""
        for window_id in self.windows:
            if self.windows[window_id]['minimized']:
                self.restore_window(window_id)
