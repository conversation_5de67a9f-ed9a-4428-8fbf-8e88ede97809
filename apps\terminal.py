"""
Terminal Application - Command line interface
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import os
import threading
import platform
import sys

from ui.window import CustomWindow

class Terminal:
    def __init__(self, parent, window_manager, window_id):
        self.parent = parent
        self.window_manager = window_manager
        self.window_id = window_id
        
        self.current_directory = os.getcwd()
        self.command_history = []
        self.history_index = -1
        self.running_process = None
        
        self.create_window()
        self.create_interface()
        self.show_welcome()
        
    def create_window(self):
        """Create the terminal window"""
        self.custom_window = CustomWindow(
            self.parent,
            title="Terminal",
            width=800,
            height=500
        )
        self.window = self.custom_window.get_window()
        self.content_frame = self.custom_window.get_content_frame()
        
    def create_interface(self):
        """Create the terminal interface"""
        # Main terminal frame
        terminal_frame = tk.Frame(self.content_frame, bg='black')
        terminal_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Terminal output area
        self.output_text = scrolledtext.ScrolledText(
            terminal_frame,
            bg='black',
            fg='#00ff00',  # Green text
            font=('Consolas', 11),
            insertbackground='#00ff00',  # Green cursor
            selectbackground='#333333',
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.output_text.pack(fill=tk.BOTH, expand=True)
        
        # Command input frame
        input_frame = tk.Frame(terminal_frame, bg='black')
        input_frame.pack(fill=tk.X, pady=(5, 0))
        
        # Prompt label
        self.prompt_label = tk.Label(
            input_frame,
            text=self.get_prompt(),
            bg='black',
            fg='#00ff00',
            font=('Consolas', 11),
            anchor='w'
        )
        self.prompt_label.pack(side=tk.LEFT)
        
        # Command entry
        self.command_var = tk.StringVar()
        self.command_entry = tk.Entry(
            input_frame,
            textvariable=self.command_var,
            bg='black',
            fg='#00ff00',
            font=('Consolas', 11),
            insertbackground='#00ff00',
            relief=tk.FLAT,
            bd=0
        )
        self.command_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.command_entry.focus_set()
        
        # Bind events
        self.command_entry.bind('<Return>', self.execute_command)
        self.command_entry.bind('<Up>', self.history_up)
        self.command_entry.bind('<Down>', self.history_down)
        self.command_entry.bind('<Tab>', self.tab_completion)
        
        # Context menu
        self.create_context_menu()
        self.output_text.bind('<Button-3>', self.show_context_menu)
        
    def create_context_menu(self):
        """Create context menu"""
        self.context_menu = tk.Menu(self.window, tearoff=0)
        self.context_menu.add_command(label="Copy", command=self.copy_text)
        self.context_menu.add_command(label="Paste", command=self.paste_text)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Clear", command=self.clear_terminal)
        self.context_menu.add_command(label="Select All", command=self.select_all)
        
    def get_prompt(self):
        """Get command prompt string"""
        user = os.environ.get('USERNAME', 'user')
        hostname = platform.node()
        current_dir = os.path.basename(self.current_directory) or self.current_directory
        return f"{user}@{hostname}:{current_dir}$ "
        
    def show_welcome(self):
        """Show welcome message"""
        welcome_msg = f"""Python OS Terminal v1.0
Running on {platform.system()} {platform.release()}
Python {sys.version}

Type 'help' for available commands.
Type 'exit' to close the terminal.

"""
        self.append_output(welcome_msg)
        
    def append_output(self, text, color='#00ff00'):
        """Append text to output area"""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.insert(tk.END, text)
        self.output_text.see(tk.END)
        self.output_text.config(state=tk.DISABLED)
        
    def execute_command(self, event=None):
        """Execute the entered command"""
        command = self.command_var.get().strip()
        if not command:
            return
            
        # Add to history
        if command not in self.command_history:
            self.command_history.append(command)
        self.history_index = len(self.command_history)
        
        # Show command in output
        self.append_output(f"{self.get_prompt()}{command}\n")
        
        # Clear input
        self.command_var.set("")
        
        # Handle built-in commands
        if self.handle_builtin_command(command):
            return
            
        # Execute external command
        self.execute_external_command(command)
        
    def handle_builtin_command(self, command):
        """Handle built-in terminal commands"""
        parts = command.split()
        cmd = parts[0].lower()
        
        if cmd == 'help':
            self.show_help()
            return True
            
        elif cmd == 'exit':
            self.window.destroy()
            return True
            
        elif cmd == 'clear':
            self.clear_terminal()
            return True
            
        elif cmd == 'cd':
            self.change_directory(parts[1] if len(parts) > 1 else os.path.expanduser('~'))
            return True
            
        elif cmd == 'pwd':
            self.append_output(f"{self.current_directory}\n")
            return True
            
        elif cmd == 'ls' or cmd == 'dir':
            self.list_directory()
            return True
            
        elif cmd == 'python':
            if len(parts) == 1:
                self.append_output("Python interactive mode not supported in this terminal.\n")
                self.append_output("Use 'python script.py' to run Python scripts.\n")
            else:
                return False  # Let external command handler deal with it
            return True
            
        elif cmd == 'echo':
            text = ' '.join(parts[1:])
            self.append_output(f"{text}\n")
            return True
            
        elif cmd == 'whoami':
            self.append_output(f"{os.environ.get('USERNAME', 'user')}\n")
            return True
            
        elif cmd == 'date':
            import datetime
            self.append_output(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            return True
            
        return False
        
    def execute_external_command(self, command):
        """Execute external command"""
        def run_command():
            try:
                # Use shell=True for Windows compatibility
                process = subprocess.Popen(
                    command,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    cwd=self.current_directory,
                    universal_newlines=True
                )
                
                self.running_process = process
                
                # Read output line by line
                for line in process.stdout:
                    self.window.after(0, lambda l=line: self.append_output(l))
                    
                # Wait for process to complete
                process.wait()
                
                if process.returncode != 0:
                    self.window.after(0, lambda: self.append_output(f"\nProcess exited with code {process.returncode}\n"))
                    
            except Exception as e:
                error_msg = f"Error executing command: {e}\n"
                self.window.after(0, lambda: self.append_output(error_msg, '#ff0000'))
            finally:
                self.running_process = None
                self.window.after(0, self.update_prompt)
                
        # Run command in background thread
        thread = threading.Thread(target=run_command, daemon=True)
        thread.start()
        
    def change_directory(self, path):
        """Change current directory"""
        try:
            if path == '..':
                new_path = os.path.dirname(self.current_directory)
            elif path == '~':
                new_path = os.path.expanduser('~')
            elif os.path.isabs(path):
                new_path = path
            else:
                new_path = os.path.join(self.current_directory, path)
                
            new_path = os.path.abspath(new_path)
            
            if os.path.exists(new_path) and os.path.isdir(new_path):
                self.current_directory = new_path
                os.chdir(new_path)
                self.update_prompt()
            else:
                self.append_output(f"cd: {path}: No such file or directory\n")
                
        except Exception as e:
            self.append_output(f"cd: {e}\n")
            
    def list_directory(self):
        """List directory contents"""
        try:
            items = os.listdir(self.current_directory)
            items.sort()
            
            if not items:
                self.append_output("(empty directory)\n")
                return
                
            # Simple listing
            for item in items:
                item_path = os.path.join(self.current_directory, item)
                if os.path.isdir(item_path):
                    self.append_output(f"{item}/\n")
                else:
                    self.append_output(f"{item}\n")
                    
        except Exception as e:
            self.append_output(f"ls: {e}\n")
            
    def update_prompt(self):
        """Update the command prompt"""
        self.prompt_label.config(text=self.get_prompt())
        
    def show_help(self):
        """Show help information"""
        help_text = """Built-in commands:
  help      - Show this help message
  exit      - Close the terminal
  clear     - Clear the terminal screen
  cd <dir>  - Change directory
  pwd       - Print working directory
  ls/dir    - List directory contents
  echo <text> - Print text
  whoami    - Print current user
  date      - Print current date and time

You can also run any system command or program.
Use Ctrl+C to interrupt running processes.
Use Up/Down arrows to navigate command history.

"""
        self.append_output(help_text)
        
    def clear_terminal(self):
        """Clear terminal output"""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.delete(1.0, tk.END)
        self.output_text.config(state=tk.DISABLED)
        
    def history_up(self, event):
        """Navigate up in command history"""
        if self.command_history and self.history_index > 0:
            self.history_index -= 1
            self.command_var.set(self.command_history[self.history_index])
            
    def history_down(self, event):
        """Navigate down in command history"""
        if self.command_history and self.history_index < len(self.command_history) - 1:
            self.history_index += 1
            self.command_var.set(self.command_history[self.history_index])
        elif self.history_index >= len(self.command_history) - 1:
            self.history_index = len(self.command_history)
            self.command_var.set("")
            
    def tab_completion(self, event):
        """Basic tab completion"""
        current_text = self.command_var.get()
        cursor_pos = self.command_entry.index(tk.INSERT)
        
        # Simple file/directory completion
        if ' ' not in current_text or current_text.startswith('cd '):
            try:
                if current_text.startswith('cd '):
                    prefix = current_text[3:]
                else:
                    prefix = current_text
                    
                if not prefix:
                    items = os.listdir(self.current_directory)
                else:
                    dir_path = os.path.dirname(prefix) or self.current_directory
                    basename = os.path.basename(prefix)
                    items = [f for f in os.listdir(dir_path) if f.startswith(basename)]
                    
                if len(items) == 1:
                    if current_text.startswith('cd '):
                        self.command_var.set(f"cd {items[0]}")
                    else:
                        self.command_var.set(items[0])
                        
            except:
                pass
                
        return 'break'  # Prevent default tab behavior
        
    def show_context_menu(self, event):
        """Show context menu"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
            
    def copy_text(self):
        """Copy selected text"""
        try:
            selected_text = self.output_text.selection_get()
            self.window.clipboard_clear()
            self.window.clipboard_append(selected_text)
        except:
            pass
            
    def paste_text(self):
        """Paste text to command entry"""
        try:
            clipboard_text = self.window.clipboard_get()
            current_pos = self.command_entry.index(tk.INSERT)
            current_text = self.command_var.get()
            new_text = current_text[:current_pos] + clipboard_text + current_text[current_pos:]
            self.command_var.set(new_text)
        except:
            pass
            
    def select_all(self):
        """Select all text in output"""
        self.output_text.tag_add(tk.SEL, "1.0", tk.END)
        
    def cleanup(self):
        """Cleanup when closing"""
        if self.running_process:
            try:
                self.running_process.terminate()
            except:
                pass
