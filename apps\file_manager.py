"""
ULTIMATE FILE MANAGER PRO - Advanced file and directory management with 100+ features
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog, colorchooser
import os
import shutil
import subprocess
import platform
import zipfile
import tarfile
import json
import csv
import hashlib
import mimetypes
import threading
import time
import tempfile
import webbrowser
import base64
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
import stat
import fnmatch
import re

from ui.window import CustomWindow

try:
    import send2trash
    SEND2TRASH_AVAILABLE = True
except ImportError:
    SEND2TRASH_AVAILABLE = False

try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

class FileManager:
    def __init__(self, parent, window_manager, window_id):
        self.parent = parent
        self.window_manager = window_manager
        self.window_id = window_id

        # Basic navigation
        self.current_path = os.path.expanduser("~")  # Start in home directory
        self.history = [self.current_path]
        self.history_index = 0

        # Advanced features
        self.clipboard = []  # For copy/cut operations
        self.clipboard_operation = None  # 'copy' or 'cut'
        self.bookmarks = []  # Favorite locations
        self.recent_files = []  # Recently accessed files
        self.search_results = []  # Search results
        self.view_mode = "details"  # details, icons, list
        self.show_hidden = False  # Show hidden files
        self.sort_by = "name"  # name, size, date, type
        self.sort_reverse = False

        # File operations
        self.selected_files = []
        self.file_operations_queue = []
        self.operation_progress = {}

        # Archive support
        self.supported_archives = ['.zip', '.tar', '.tar.gz', '.tar.bz2', '.rar', '.7z']

        # Image preview
        self.preview_cache = {}

        # Database for file indexing
        self.init_database()

        # Load settings
        self.load_settings()

        self.create_window()
        self.create_interface()
        self.refresh_view()

        # Start background services
        self.start_background_services()

    def init_database(self):
        """Initialize SQLite database for file indexing"""
        try:
            os.makedirs('data', exist_ok=True)
            self.db_path = 'data/file_manager.db'

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Create tables
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS files (
                    id INTEGER PRIMARY KEY,
                    filepath TEXT UNIQUE,
                    filename TEXT,
                    size INTEGER,
                    modified_time TIMESTAMP,
                    file_type TEXT,
                    checksum TEXT,
                    tags TEXT,
                    description TEXT,
                    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS bookmarks (
                    id INTEGER PRIMARY KEY,
                    name TEXT,
                    path TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS recent_files (
                    id INTEGER PRIMARY KEY,
                    filepath TEXT,
                    accessed_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"Database initialization error: {e}")

    def load_settings(self):
        """Load user settings"""
        try:
            settings_file = 'data/file_manager_settings.json'
            if os.path.exists(settings_file):
                with open(settings_file, 'r') as f:
                    settings = json.load(f)

                self.view_mode = settings.get('view_mode', 'details')
                self.show_hidden = settings.get('show_hidden', False)
                self.sort_by = settings.get('sort_by', 'name')
                self.sort_reverse = settings.get('sort_reverse', False)
                self.bookmarks = settings.get('bookmarks', [])

        except Exception as e:
            print(f"Settings loading error: {e}")

    def save_settings(self):
        """Save user settings"""
        try:
            os.makedirs('data', exist_ok=True)
            settings = {
                'view_mode': self.view_mode,
                'show_hidden': self.show_hidden,
                'sort_by': self.sort_by,
                'sort_reverse': self.sort_reverse,
                'bookmarks': self.bookmarks
            }

            with open('data/file_manager_settings.json', 'w') as f:
                json.dump(settings, f, indent=4)

        except Exception as e:
            print(f"Settings saving error: {e}")

    def start_background_services(self):
        """Start background services"""
        # Start file indexing service
        threading.Thread(target=self.index_files_background, daemon=True).start()

    def create_window(self):
        """Create the ultimate file manager window"""
        self.custom_window = CustomWindow(
            self.parent,
            title="🗂️ ULTIMATE FILE MANAGER PRO",
            width=1400,
            height=900
        )
        self.window = self.custom_window.get_window()
        self.content_frame = self.custom_window.get_content_frame()

        # Bind keyboard shortcuts
        self.setup_keyboard_shortcuts()

    def create_interface(self):
        """Create the ultimate file manager interface"""
        # Create menu bar
        self.create_enhanced_menu()

        # Create main notebook for tabs
        self.main_notebook = ttk.Notebook(self.content_frame)
        self.main_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create tabs
        self.create_file_browser_tab()
        self.create_search_tab()
        self.create_archive_tab()
        self.create_tools_tab()
        self.create_settings_tab()

        # Create status bar
        self.create_enhanced_status_bar()

    def create_enhanced_menu(self):
        """Create enhanced menu bar"""
        menubar = tk.Menu(self.window)
        self.window.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Folder", command=self.create_folder, accelerator="Ctrl+Shift+N")
        file_menu.add_command(label="New Text File", command=self.create_text_file, accelerator="Ctrl+N")
        file_menu.add_command(label="New Archive", command=self.create_archive)
        file_menu.add_separator()
        file_menu.add_command(label="Open", command=self.open_selected, accelerator="Enter")
        file_menu.add_command(label="Open With...", command=self.open_with)
        file_menu.add_command(label="Open in Terminal", command=self.open_in_terminal)
        file_menu.add_separator()
        file_menu.add_command(label="Properties", command=self.show_properties, accelerator="Alt+Enter")
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.window.destroy, accelerator="Alt+F4")

        # Edit menu
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Select All", command=self.select_all, accelerator="Ctrl+A")
        edit_menu.add_command(label="Invert Selection", command=self.invert_selection, accelerator="Ctrl+I")
        edit_menu.add_separator()
        edit_menu.add_command(label="Copy", command=self.copy_selected, accelerator="Ctrl+C")
        edit_menu.add_command(label="Cut", command=self.cut_selected, accelerator="Ctrl+X")
        edit_menu.add_command(label="Paste", command=self.paste_here, accelerator="Ctrl+V")
        edit_menu.add_separator()
        edit_menu.add_command(label="Rename", command=self.rename_selected, accelerator="F2")
        edit_menu.add_command(label="Delete", command=self.delete_selected, accelerator="Delete")
        edit_menu.add_command(label="Move to Trash", command=self.move_to_trash, accelerator="Shift+Delete")

        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Refresh", command=self.refresh_view, accelerator="F5")
        view_menu.add_separator()
        view_menu.add_command(label="Details View", command=lambda: self.set_view_mode("details"))
        view_menu.add_command(label="Icons View", command=lambda: self.set_view_mode("icons"))
        view_menu.add_command(label="List View", command=lambda: self.set_view_mode("list"))
        view_menu.add_separator()
        view_menu.add_checkbutton(label="Show Hidden Files", command=self.toggle_hidden_files)
        view_menu.add_checkbutton(label="Show File Extensions", command=self.toggle_extensions)
        view_menu.add_separator()
        view_menu.add_command(label="Sort by Name", command=lambda: self.set_sort("name"))
        view_menu.add_command(label="Sort by Size", command=lambda: self.set_sort("size"))
        view_menu.add_command(label="Sort by Date", command=lambda: self.set_sort("date"))
        view_menu.add_command(label="Sort by Type", command=lambda: self.set_sort("type"))

        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Search Files", command=self.open_search, accelerator="Ctrl+F")
        tools_menu.add_command(label="Find Duplicates", command=self.find_duplicates)
        tools_menu.add_command(label="Disk Usage", command=self.show_disk_usage)
        tools_menu.add_separator()
        tools_menu.add_command(label="Create Archive", command=self.create_archive_dialog)
        tools_menu.add_command(label="Extract Archive", command=self.extract_archive)
        tools_menu.add_separator()
        tools_menu.add_command(label="Calculate Checksums", command=self.calculate_checksums)
        tools_menu.add_command(label="Compare Files", command=self.compare_files)
        tools_menu.add_command(label="Batch Rename", command=self.batch_rename)
        tools_menu.add_separator()
        tools_menu.add_command(label="Secure Delete", command=self.secure_delete)
        tools_menu.add_command(label="File Shredder", command=self.file_shredder)

    def create_file_browser_tab(self):
        """Create the main file browser tab"""
        browser_frame = ttk.Frame(self.main_notebook)
        self.main_notebook.add(browser_frame, text="📁 File Browser")

        # Create enhanced toolbar
        self.create_enhanced_toolbar(browser_frame)

        # Create main paned window
        main_paned = ttk.PanedWindow(browser_frame, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel - Enhanced sidebar
        self.create_enhanced_sidebar(main_paned)

        # Right panel - File view and preview
        right_panel = ttk.PanedWindow(main_paned, orient=tk.VERTICAL)
        main_paned.add(right_panel, weight=3)

        # File list area
        self.create_enhanced_file_view(right_panel)

        # Preview panel
        self.create_preview_panel(right_panel)

    def create_enhanced_toolbar(self, parent):
        """Create enhanced toolbar with more features"""
        toolbar = tk.Frame(parent, bg='#e9ecef', height=50)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        toolbar.pack_propagate(False)

        # Navigation buttons
        nav_frame = tk.Frame(toolbar, bg='#e9ecef')
        nav_frame.pack(side=tk.LEFT, padx=5)

        tk.Button(nav_frame, text="⬅", command=self.go_back, width=3, font=("Arial", 12)).pack(side=tk.LEFT, padx=1)
        tk.Button(nav_frame, text="➡", command=self.go_forward, width=3, font=("Arial", 12)).pack(side=tk.LEFT, padx=1)
        tk.Button(nav_frame, text="⬆", command=self.go_up, width=3, font=("Arial", 12)).pack(side=tk.LEFT, padx=1)
        tk.Button(nav_frame, text="🏠", command=self.go_home, width=3, font=("Arial", 12)).pack(side=tk.LEFT, padx=1)
        tk.Button(nav_frame, text="🔄", command=self.refresh_view, width=3, font=("Arial", 12)).pack(side=tk.LEFT, padx=1)

        # Address bar
        address_frame = tk.Frame(toolbar, bg='#e9ecef')
        address_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=10)

        tk.Label(address_frame, text="📍", bg='#e9ecef', font=("Arial", 12)).pack(side=tk.LEFT)
        self.address_var = tk.StringVar(value=self.current_path)
        self.address_entry = tk.Entry(address_frame, textvariable=self.address_var, font=("Arial", 10))
        self.address_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.address_entry.bind('<Return>', self.navigate_to_address)

        # Action buttons
        action_frame = tk.Frame(toolbar, bg='#e9ecef')
        action_frame.pack(side=tk.RIGHT, padx=5)

        tk.Button(action_frame, text="📁 New Folder", command=self.create_folder, font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
        tk.Button(action_frame, text="📄 New File", command=self.create_text_file, font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
        tk.Button(action_frame, text="📦 Archive", command=self.create_archive_dialog, font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
        tk.Button(action_frame, text="🔍 Search", command=self.open_search, font=("Arial", 9)).pack(side=tk.LEFT, padx=2)

    def create_enhanced_sidebar(self, parent):
        """Create enhanced sidebar with bookmarks and quick access"""
        sidebar = ttk.Frame(parent)
        parent.add(sidebar, weight=1)

        # Sidebar notebook
        sidebar_notebook = ttk.Notebook(sidebar)
        sidebar_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Quick Access tab
        quick_frame = ttk.Frame(sidebar_notebook)
        sidebar_notebook.add(quick_frame, text="Quick Access")

        # System locations
        system_frame = tk.LabelFrame(quick_frame, text="System", font=("Arial", 10, "bold"))
        system_frame.pack(fill=tk.X, padx=5, pady=5)

        quick_paths = [
            ("🏠 Home", os.path.expanduser("~")),
            ("🖥️ Desktop", os.path.join(os.path.expanduser("~"), "Desktop")),
            ("📄 Documents", os.path.join(os.path.expanduser("~"), "Documents")),
            ("📥 Downloads", os.path.join(os.path.expanduser("~"), "Downloads")),
            ("🎵 Music", os.path.join(os.path.expanduser("~"), "Music")),
            ("🖼️ Pictures", os.path.join(os.path.expanduser("~"), "Pictures")),
            ("🎬 Videos", os.path.join(os.path.expanduser("~"), "Videos")),
        ]

        for name, path in quick_paths:
            if os.path.exists(path):
                btn = tk.Button(
                    system_frame,
                    text=name,
                    command=lambda p=path: self.navigate_to(p),
                    bg='white',
                    relief=tk.FLAT,
                    anchor='w',
                    padx=10,
                    font=("Arial", 9)
                )
                btn.pack(fill=tk.X, padx=2, pady=1)

        # Bookmarks tab
        bookmarks_frame = ttk.Frame(sidebar_notebook)
        sidebar_notebook.add(bookmarks_frame, text="Bookmarks")

        # Bookmarks controls
        bookmark_controls = tk.Frame(bookmarks_frame)
        bookmark_controls.pack(fill=tk.X, padx=5, pady=5)

        tk.Button(bookmark_controls, text="➕", command=self.add_bookmark, width=3).pack(side=tk.LEFT, padx=2)
        tk.Button(bookmark_controls, text="➖", command=self.remove_bookmark, width=3).pack(side=tk.LEFT, padx=2)

        # Bookmarks list
        self.bookmarks_listbox = tk.Listbox(bookmarks_frame, font=("Arial", 9))
        self.bookmarks_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.bookmarks_listbox.bind('<Double-1>', self.navigate_to_bookmark)

        # Recent files tab
        recent_frame = ttk.Frame(sidebar_notebook)
        sidebar_notebook.add(recent_frame, text="Recent")

        self.recent_listbox = tk.Listbox(recent_frame, font=("Arial", 9))
        self.recent_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.recent_listbox.bind('<Double-1>', self.open_recent_file)

    def create_enhanced_file_view(self, parent):
        """Create enhanced file view with multiple view modes"""
        file_view_frame = ttk.Frame(parent)
        parent.add(file_view_frame, weight=2)

        # View controls
        view_controls = tk.Frame(file_view_frame)
        view_controls.pack(fill=tk.X, padx=5, pady=5)

        # View mode buttons
        view_mode_frame = tk.Frame(view_controls)
        view_mode_frame.pack(side=tk.LEFT)

        tk.Button(view_mode_frame, text="📋", command=lambda: self.set_view_mode("details"), width=3).pack(side=tk.LEFT, padx=1)
        tk.Button(view_mode_frame, text="🔲", command=lambda: self.set_view_mode("icons"), width=3).pack(side=tk.LEFT, padx=1)
        tk.Button(view_mode_frame, text="📄", command=lambda: self.set_view_mode("list"), width=3).pack(side=tk.LEFT, padx=1)

        # Sort controls
        sort_frame = tk.Frame(view_controls)
        sort_frame.pack(side=tk.RIGHT)

        tk.Label(sort_frame, text="Sort:").pack(side=tk.LEFT)
        self.sort_var = tk.StringVar(value=self.sort_by)
        sort_combo = ttk.Combobox(sort_frame, textvariable=self.sort_var, values=["name", "size", "date", "type"], width=10)
        sort_combo.pack(side=tk.LEFT, padx=5)
        sort_combo.bind('<<ComboboxSelected>>', self.on_sort_change)

        # File list
        list_frame = tk.Frame(file_view_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create treeview for file list
        columns = ('Name', 'Size', 'Type', 'Modified', 'Permissions')
        self.file_tree = ttk.Treeview(list_frame, columns=columns, show='tree headings')

        # Configure columns
        self.file_tree.heading('#0', text='')
        self.file_tree.column('#0', width=30)

        column_widths = {'Name': 300, 'Size': 100, 'Type': 100, 'Modified': 150, 'Permissions': 100}
        for col in columns:
            self.file_tree.heading(col, text=col, command=lambda c=col: self.sort_by_column(c))
            self.file_tree.column(col, width=column_widths.get(col, 100))

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.file_tree.xview)
        self.file_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack treeview and scrollbars
        self.file_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Bind events
        self.file_tree.bind('<Double-1>', self.on_double_click)
        self.file_tree.bind('<Button-3>', self.show_context_menu)
        self.file_tree.bind('<Button-1>', self.on_file_select)
        self.file_tree.bind('<Control-a>', self.select_all)

        # Create context menu
        self.create_enhanced_context_menu()

    def create_preview_panel(self, parent):
        """Create file preview panel"""
        preview_frame = ttk.Frame(parent)
        parent.add(preview_frame, weight=1)

        tk.Label(preview_frame, text="File Preview", font=("Arial", 12, "bold")).pack(pady=5)

        # Preview content area
        self.preview_content = tk.Frame(preview_frame, bg='white', relief=tk.SUNKEN, bd=1)
        self.preview_content.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Default preview message
        self.preview_label = tk.Label(self.preview_content, text="Select a file to preview", bg='white', fg='gray')
        self.preview_label.pack(expand=True)

    def create_toolbar(self):
        """Create the toolbar"""
        toolbar = tk.Frame(self.content_frame, bg='#e9ecef', height=40)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        toolbar.pack_propagate(False)

        # Navigation buttons
        tk.Button(toolbar, text="←", command=self.go_back, width=3).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar, text="→", command=self.go_forward, width=3).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar, text="↑", command=self.go_up, width=3).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar, text="🏠", command=self.go_home, width=3).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar, text="🔄", command=self.refresh_view, width=3).pack(side=tk.LEFT, padx=2)

        # Address bar
        tk.Label(toolbar, text="Path:", bg='#e9ecef').pack(side=tk.LEFT, padx=(10, 5))
        self.address_var = tk.StringVar(value=self.current_path)
        self.address_entry = tk.Entry(toolbar, textvariable=self.address_var, width=50)
        self.address_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.address_entry.bind('<Return>', self.navigate_to_address)

        # Action buttons
        tk.Button(toolbar, text="New Folder", command=self.create_folder).pack(side=tk.RIGHT, padx=2)

    def create_context_menu(self):
        """Create context menu for files"""
        self.context_menu = tk.Menu(self.window, tearoff=0)
        self.context_menu.add_command(label="Open", command=self.open_selected)
        self.context_menu.add_command(label="Open with...", command=self.open_with)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Copy", command=self.copy_selected)
        self.context_menu.add_command(label="Cut", command=self.cut_selected)
        self.context_menu.add_command(label="Paste", command=self.paste_here)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Rename", command=self.rename_selected)
        self.context_menu.add_command(label="Delete", command=self.delete_selected)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Properties", command=self.show_properties)

    def refresh_view(self):
        """Refresh the file list"""
        # Clear current items
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        try:
            # Update address bar
            self.address_var.set(self.current_path)

            # Get directory contents
            items = []

            # Add parent directory if not at root
            if self.current_path != os.path.dirname(self.current_path):
                items.append(('..',  'folder', '', ''))

            # Get files and folders
            try:
                for item in os.listdir(self.current_path):
                    item_path = os.path.join(self.current_path, item)

                    if os.path.isdir(item_path):
                        items.append((item, 'folder', '', self.get_modified_time(item_path)))
                    else:
                        size = self.format_size(os.path.getsize(item_path))
                        ext = os.path.splitext(item)[1] or 'File'
                        items.append((item, 'file', size, self.get_modified_time(item_path)))

            except PermissionError:
                messagebox.showerror("Error", "Permission denied")
                return

            # Sort items (folders first, then files)
            items.sort(key=lambda x: (x[1] != 'folder', x[0].lower()))

            # Add items to tree
            for name, item_type, size, modified in items:
                icon = '📁' if item_type == 'folder' else '📄'
                self.file_tree.insert('', 'end', text=icon, values=(name, size, item_type, modified))

            # Update status
            file_count = sum(1 for _, t, _, _ in items if t == 'file')
            folder_count = sum(1 for _, t, _, _ in items if t == 'folder')
            self.status_bar.config(text=f"{folder_count} folders, {file_count} files")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh view: {e}")

    def navigate_to(self, path):
        """Navigate to a specific path"""
        if os.path.exists(path) and os.path.isdir(path):
            self.current_path = os.path.abspath(path)

            # Update history
            if self.history_index < len(self.history) - 1:
                self.history = self.history[:self.history_index + 1]
            self.history.append(self.current_path)
            self.history_index = len(self.history) - 1

            self.refresh_view()
        else:
            messagebox.showerror("Error", f"Path does not exist: {path}")

    def navigate_to_address(self, event=None):
        """Navigate to address bar path"""
        path = self.address_var.get()
        self.navigate_to(path)

    def on_double_click(self, event):
        """Handle double-click on file/folder"""
        selection = self.file_tree.selection()
        if selection:
            item = self.file_tree.item(selection[0])
            name = item['values'][0]

            if name == '..':
                self.go_up()
            else:
                item_path = os.path.join(self.current_path, name)
                if os.path.isdir(item_path):
                    self.navigate_to(item_path)
                else:
                    self.open_file(item_path)

    def open_file(self, file_path):
        """Open a file with default application"""
        try:
            if platform.system() == 'Windows':
                os.startfile(file_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.run(['open', file_path])
            else:  # Linux
                subprocess.run(['xdg-open', file_path])
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open file: {e}")

    def go_back(self):
        """Go back in history"""
        if self.history_index > 0:
            self.history_index -= 1
            self.current_path = self.history[self.history_index]
            self.refresh_view()

    def go_forward(self):
        """Go forward in history"""
        if self.history_index < len(self.history) - 1:
            self.history_index += 1
            self.current_path = self.history[self.history_index]
            self.refresh_view()

    def go_up(self):
        """Go to parent directory"""
        parent = os.path.dirname(self.current_path)
        if parent != self.current_path:
            self.navigate_to(parent)

    def go_home(self):
        """Go to home directory"""
        self.navigate_to(os.path.expanduser("~"))

    def format_size(self, size):
        """Format file size in human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"

    def get_modified_time(self, path):
        """Get formatted modification time"""
        try:
            mtime = os.path.getmtime(path)
            return datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M")
        except:
            return ""

    def show_context_menu(self, event):
        """Show context menu"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def open_selected(self):
        """Open selected file/folder"""
        selection = self.file_tree.selection()
        if selection:
            item = self.file_tree.item(selection[0])
            name = item['values'][0]
            item_path = os.path.join(self.current_path, name)

            if os.path.isdir(item_path):
                self.navigate_to(item_path)
            else:
                self.open_file(item_path)

    def open_with(self):
        """Open file with specific application"""
        messagebox.showinfo("Info", "Open with functionality not implemented yet")

    def open_in_terminal(self):
        """Open terminal in current directory"""
        try:
            # Import Terminal class
            from apps.terminal import Terminal

            # Create new terminal window in current directory
            terminal = Terminal(self.parent, self.window_manager, f"terminal_{int(time.time())}")

            # Change to current directory in the terminal
            if hasattr(terminal, 'change_directory'):
                terminal.change_directory(self.current_path)

            # If selected file/folder, navigate to its directory
            selection = self.file_tree.selection()
            if selection:
                item = self.file_tree.item(selection[0])
                name = item['values'][0]
                if name != '..':
                    item_path = os.path.join(self.current_path, name)
                    if os.path.isdir(item_path):
                        terminal.change_directory(item_path)
                    else:
                        # For files, open terminal in the file's directory
                        terminal.change_directory(os.path.dirname(item_path))

        except Exception as e:
            messagebox.showerror("Error", f"Failed to open terminal: {e}")

    def copy_selected(self):
        """Copy selected file/folder"""
        messagebox.showinfo("Info", "Copy functionality not implemented yet")

    def cut_selected(self):
        """Cut selected file/folder"""
        messagebox.showinfo("Info", "Cut functionality not implemented yet")

    def paste_here(self):
        """Paste file/folder here"""
        messagebox.showinfo("Info", "Paste functionality not implemented yet")

    def rename_selected(self):
        """Rename selected file/folder"""
        messagebox.showinfo("Info", "Rename functionality not implemented yet")

    def delete_selected(self):
        """Delete selected file/folder"""
        selection = self.file_tree.selection()
        if selection:
            item = self.file_tree.item(selection[0])
            name = item['values'][0]

            if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{name}'?"):
                try:
                    item_path = os.path.join(self.current_path, name)
                    if os.path.isdir(item_path):
                        shutil.rmtree(item_path)
                    else:
                        os.remove(item_path)
                    self.refresh_view()
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to delete: {e}")

    def show_properties(self):
        """Show file/folder properties"""
        messagebox.showinfo("Info", "Properties functionality not implemented yet")

    def select_all(self, event=None):
        """Select all files in current directory"""
        try:
            # Select all items in the tree
            all_items = self.file_tree.get_children()
            self.file_tree.selection_set(all_items)

            # Update selected files list
            self.selected_files = []
            for item_id in all_items:
                item = self.file_tree.item(item_id)
                name = item['values'][0]
                if name != '..':  # Don't include parent directory
                    item_path = os.path.join(self.current_path, name)
                    self.selected_files.append(item_path)

        except Exception as e:
            print(f"Error selecting all: {e}")

    def invert_selection(self, event=None):
        """Invert current selection"""
        try:
            current_selection = set(self.file_tree.selection())
            all_items = set(self.file_tree.get_children())

            # Calculate inverted selection
            new_selection = all_items - current_selection

            # Apply new selection
            self.file_tree.selection_set(list(new_selection))

            # Update selected files list
            self.selected_files = []
            for item_id in new_selection:
                item = self.file_tree.item(item_id)
                name = item['values'][0]
                if name != '..':  # Don't include parent directory
                    item_path = os.path.join(self.current_path, name)
                    self.selected_files.append(item_path)

        except Exception as e:
            print(f"Error inverting selection: {e}")

    def move_to_trash(self):
        """Move selected files to trash"""
        selection = self.file_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select files to move to trash")
            return

        if SEND2TRASH_AVAILABLE:
            try:
                for item_id in selection:
                    item = self.file_tree.item(item_id)
                    name = item['values'][0]
                    if name != '..':
                        item_path = os.path.join(self.current_path, name)
                        send2trash.send2trash(item_path)

                self.refresh_view()
                messagebox.showinfo("Success", f"Moved {len(selection)} item(s) to trash")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to move to trash: {e}")
        else:
            messagebox.showwarning("Warning", "Trash functionality not available. Use Delete instead.")

    def on_file_select(self, event=None):
        """Handle file selection"""
        try:
            selection = self.file_tree.selection()
            self.selected_files = []

            for item_id in selection:
                item = self.file_tree.item(item_id)
                name = item['values'][0]
                if name != '..':  # Don't include parent directory
                    item_path = os.path.join(self.current_path, name)
                    self.selected_files.append(item_path)

        except Exception as e:
            print(f"Error handling file selection: {e}")

    def create_folder(self):
        """Create new folder"""
        name = tk.simpledialog.askstring("New Folder", "Enter folder name:")
        if name:
            try:
                folder_path = os.path.join(self.current_path, name)
                os.makedirs(folder_path)
                self.refresh_view()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to create folder: {e}")

    # ==================== NEW ENHANCED METHODS ====================

    def setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts"""
        self.window.bind('<Control-n>', lambda e: self.create_text_file())
        self.window.bind('<Control-Shift-N>', lambda e: self.create_folder())
        self.window.bind('<Control-c>', lambda e: self.copy_selected())
        self.window.bind('<Control-x>', lambda e: self.cut_selected())
        self.window.bind('<Control-v>', lambda e: self.paste_here())
        self.window.bind('<Control-a>', lambda e: self.select_all())
        self.window.bind('<Control-i>', lambda e: self.invert_selection())
        self.window.bind('<Control-f>', lambda e: self.open_search())
        self.window.bind('<F2>', lambda e: self.rename_selected())
        self.window.bind('<F5>', lambda e: self.refresh_view())
        self.window.bind('<Delete>', lambda e: self.delete_selected())
        self.window.bind('<Shift-Delete>', lambda e: self.move_to_trash())
        self.window.bind('<Alt-Return>', lambda e: self.show_properties())

    def create_text_file(self):
        """Create a new text file"""
        name = simpledialog.askstring("New Text File", "Enter file name (with .txt extension):")
        if name:
            if not name.endswith('.txt'):
                name += '.txt'
            try:
                file_path = os.path.join(self.current_path, name)
                with open(file_path, 'w') as f:
                    f.write("# New Text File\n\nContent goes here...")
                self.refresh_view()
                messagebox.showinfo("Success", f"Text file '{name}' created successfully!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to create text file: {e}")

    def create_archive_dialog(self):
        """Show dialog to create archive"""
        if not self.selected_files:
            messagebox.showwarning("Warning", "Please select files to archive")
            return

        # Create archive dialog
        dialog = tk.Toplevel(self.window)
        dialog.title("Create Archive")
        dialog.geometry("400x300")
        dialog.transient(self.window)
        dialog.grab_set()

        # Archive name
        tk.Label(dialog, text="Archive Name:", font=("Arial", 10, "bold")).pack(pady=5)
        name_var = tk.StringVar(value="archive.zip")
        name_entry = tk.Entry(dialog, textvariable=name_var, width=40)
        name_entry.pack(pady=5)

        # Archive type
        tk.Label(dialog, text="Archive Type:", font=("Arial", 10, "bold")).pack(pady=5)
        type_var = tk.StringVar(value="zip")
        type_frame = tk.Frame(dialog)
        type_frame.pack(pady=5)

        tk.Radiobutton(type_frame, text="ZIP", variable=type_var, value="zip").pack(side=tk.LEFT, padx=10)
        tk.Radiobutton(type_frame, text="TAR", variable=type_var, value="tar").pack(side=tk.LEFT, padx=10)
        tk.Radiobutton(type_frame, text="TAR.GZ", variable=type_var, value="tar.gz").pack(side=tk.LEFT, padx=10)

        # Compression level
        tk.Label(dialog, text="Compression Level:", font=("Arial", 10, "bold")).pack(pady=5)
        compression_var = tk.IntVar(value=6)
        compression_scale = tk.Scale(dialog, from_=0, to=9, orient=tk.HORIZONTAL, variable=compression_var)
        compression_scale.pack(pady=5)

        # Buttons
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=20)

        def create_archive():
            archive_name = name_var.get()
            archive_type = type_var.get()
            compression = compression_var.get()

            try:
                self.create_archive(archive_name, archive_type, compression)
                dialog.destroy()
                messagebox.showinfo("Success", f"Archive '{archive_name}' created successfully!")
                self.refresh_view()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to create archive: {e}")

        tk.Button(button_frame, text="Create", command=create_archive, bg='#28a745', fg='white', padx=20).pack(side=tk.LEFT, padx=10)
        tk.Button(button_frame, text="Cancel", command=dialog.destroy, bg='#dc3545', fg='white', padx=20).pack(side=tk.LEFT, padx=10)

    def create_archive(self, archive_name, archive_type, compression_level=6):
        """Create archive from selected files"""
        archive_path = os.path.join(self.current_path, archive_name)

        if archive_type == "zip":
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=compression_level) as zipf:
                for file_path in self.selected_files:
                    if os.path.isfile(file_path):
                        zipf.write(file_path, os.path.basename(file_path))
                    elif os.path.isdir(file_path):
                        for root, dirs, files in os.walk(file_path):
                            for file in files:
                                file_full_path = os.path.join(root, file)
                                arcname = os.path.relpath(file_full_path, os.path.dirname(file_path))
                                zipf.write(file_full_path, arcname)

        elif archive_type == "tar":
            with tarfile.open(archive_path, 'w') as tarf:
                for file_path in self.selected_files:
                    tarf.add(file_path, os.path.basename(file_path))

        elif archive_type == "tar.gz":
            with tarfile.open(archive_path, 'w:gz') as tarf:
                for file_path in self.selected_files:
                    tarf.add(file_path, os.path.basename(file_path))

    def extract_archive(self):
        """Extract selected archive"""
        selection = self.file_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an archive to extract")
            return

        item = self.file_tree.item(selection[0])
        filename = item['values'][0]
        file_path = os.path.join(self.current_path, filename)

        if not any(filename.lower().endswith(ext) for ext in self.supported_archives):
            messagebox.showwarning("Warning", "Selected file is not a supported archive")
            return

        # Ask for extraction directory
        extract_dir = filedialog.askdirectory(title="Select extraction directory", initialdir=self.current_path)
        if not extract_dir:
            return

        try:
            if filename.lower().endswith('.zip'):
                with zipfile.ZipFile(file_path, 'r') as zipf:
                    zipf.extractall(extract_dir)
            elif filename.lower().endswith(('.tar', '.tar.gz', '.tar.bz2')):
                with tarfile.open(file_path, 'r') as tarf:
                    tarf.extractall(extract_dir)

            messagebox.showinfo("Success", f"Archive extracted to: {extract_dir}")
            if extract_dir == self.current_path:
                self.refresh_view()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to extract archive: {e}")

    def open_search(self):
        """Open search dialog"""
        # Switch to search tab
        self.main_notebook.select(1)  # Search tab is index 1

    def find_duplicates(self):
        """Find duplicate files in current directory"""
        duplicates = {}
        file_hashes = {}

        def calculate_file_hash(filepath):
            hash_md5 = hashlib.md5()
            try:
                with open(filepath, "rb") as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        hash_md5.update(chunk)
                return hash_md5.hexdigest()
            except:
                return None

        # Progress dialog
        progress_dialog = tk.Toplevel(self.window)
        progress_dialog.title("Finding Duplicates...")
        progress_dialog.geometry("400x100")
        progress_dialog.transient(self.window)
        progress_dialog.grab_set()

        progress_label = tk.Label(progress_dialog, text="Scanning files...")
        progress_label.pack(pady=20)

        progress_bar = ttk.Progressbar(progress_dialog, mode='indeterminate')
        progress_bar.pack(fill=tk.X, padx=20, pady=10)
        progress_bar.start()

        def scan_files():
            try:
                for root, dirs, files in os.walk(self.current_path):
                    for file in files:
                        filepath = os.path.join(root, file)
                        file_hash = calculate_file_hash(filepath)
                        if file_hash:
                            if file_hash in file_hashes:
                                if file_hash not in duplicates:
                                    duplicates[file_hash] = [file_hashes[file_hash]]
                                duplicates[file_hash].append(filepath)
                            else:
                                file_hashes[file_hash] = filepath

                progress_dialog.destroy()
                self.show_duplicates_result(duplicates)

            except Exception as e:
                progress_dialog.destroy()
                messagebox.showerror("Error", f"Failed to find duplicates: {e}")

        threading.Thread(target=scan_files, daemon=True).start()

    def show_duplicates_result(self, duplicates):
        """Show duplicate files result"""
        if not duplicates:
            messagebox.showinfo("No Duplicates", "No duplicate files found!")
            return

        # Create result dialog
        result_dialog = tk.Toplevel(self.window)
        result_dialog.title(f"Found {len(duplicates)} sets of duplicates")
        result_dialog.geometry("600x400")
        result_dialog.transient(self.window)

        # Result tree
        tree_frame = tk.Frame(result_dialog)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        columns = ('File', 'Size', 'Path')
        result_tree = ttk.Treeview(tree_frame, columns=columns, show='tree headings')

        for col in columns:
            result_tree.heading(col, text=col)
            result_tree.column(col, width=200)

        # Add duplicates to tree
        for i, (hash_val, files) in enumerate(duplicates.items()):
            parent = result_tree.insert('', 'end', text=f"Duplicate Set {i+1}", values=('', '', ''))
            for file_path in files:
                try:
                    size = self.format_size(os.path.getsize(file_path))
                    result_tree.insert(parent, 'end', text='', values=(os.path.basename(file_path), size, file_path))
                except:
                    pass

        result_tree.pack(fill=tk.BOTH, expand=True)

        # Buttons
        button_frame = tk.Frame(result_dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Button(button_frame, text="Close", command=result_dialog.destroy).pack(side=tk.RIGHT, padx=5)

    def cleanup(self):
        """Cleanup when closing"""
        self.save_settings()
