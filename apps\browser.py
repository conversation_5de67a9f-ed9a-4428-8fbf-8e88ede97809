"""
Web Browser Application - Simple web browser with basic functionality
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import webbrowser
import urllib.request
import urllib.parse
from html.parser import HTMLParser
import threading

from ui.window import CustomWindow

class SimpleHTMLParser(HTMLParser):
    """Simple HTML parser to extract text content"""
    def __init__(self):
        super().__init__()
        self.text_content = []
        self.in_title = False
        self.title = ""
        
    def handle_starttag(self, tag, attrs):
        if tag.lower() == 'title':
            self.in_title = True
            
    def handle_endtag(self, tag):
        if tag.lower() == 'title':
            self.in_title = False
            
    def handle_data(self, data):
        if self.in_title:
            self.title += data.strip()
        else:
            clean_data = data.strip()
            if clean_data:
                self.text_content.append(clean_data)
                
    def get_text(self):
        return '\n'.join(self.text_content)

class WebBrowser:
    def __init__(self, parent, window_manager, window_id):
        self.parent = parent
        self.window_manager = window_manager
        self.window_id = window_id
        
        self.current_url = ""
        self.history = []
        self.history_index = -1
        self.bookmarks = [
            ("Google", "https://www.google.com"),
            ("GitHub", "https://www.github.com"),
            ("Python.org", "https://www.python.org"),
            ("Stack Overflow", "https://stackoverflow.com"),
        ]
        
        self.create_window()
        self.create_interface()
        self.load_homepage()
        
    def create_window(self):
        """Create the browser window"""
        self.custom_window = CustomWindow(
            self.parent,
            title="Web Browser",
            width=1000,
            height=700
        )
        self.window = self.custom_window.get_window()
        self.content_frame = self.custom_window.get_content_frame()
        
    def create_interface(self):
        """Create the browser interface"""
        # Navigation toolbar
        self.create_toolbar()
        
        # Main content area
        main_frame = tk.Frame(self.content_frame, bg='white')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Content display area
        content_frame = tk.Frame(main_frame, bg='white')
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Text widget with scrollbar for displaying web content
        text_frame = tk.Frame(content_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.content_text = tk.Text(
            text_frame,
            wrap=tk.WORD,
            bg='white',
            fg='black',
            font=('Arial', 11),
            state=tk.DISABLED
        )
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.content_text.yview)
        h_scrollbar = ttk.Scrollbar(text_frame, orient=tk.HORIZONTAL, command=self.content_text.xview)
        self.content_text.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack text widget and scrollbars
        self.content_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Status bar
        self.status_bar = tk.Label(
            self.content_frame,
            text="Ready",
            relief=tk.SUNKEN,
            anchor=tk.W,
            bg='#f8f9fa'
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def create_toolbar(self):
        """Create the navigation toolbar"""
        toolbar = tk.Frame(self.content_frame, bg='#e9ecef', height=50)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        toolbar.pack_propagate(False)
        
        # Navigation buttons
        nav_frame = tk.Frame(toolbar, bg='#e9ecef')
        nav_frame.pack(side=tk.LEFT, pady=5)
        
        self.back_btn = tk.Button(nav_frame, text="←", command=self.go_back, width=3, state=tk.DISABLED)
        self.back_btn.pack(side=tk.LEFT, padx=2)
        
        self.forward_btn = tk.Button(nav_frame, text="→", command=self.go_forward, width=3, state=tk.DISABLED)
        self.forward_btn.pack(side=tk.LEFT, padx=2)
        
        self.refresh_btn = tk.Button(nav_frame, text="🔄", command=self.refresh_page, width=3)
        self.refresh_btn.pack(side=tk.LEFT, padx=2)
        
        self.home_btn = tk.Button(nav_frame, text="🏠", command=self.load_homepage, width=3)
        self.home_btn.pack(side=tk.LEFT, padx=2)
        
        # Address bar
        address_frame = tk.Frame(toolbar, bg='#e9ecef')
        address_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=10, pady=5)
        
        tk.Label(address_frame, text="URL:", bg='#e9ecef').pack(side=tk.LEFT)
        
        self.url_var = tk.StringVar()
        self.url_entry = tk.Entry(address_frame, textvariable=self.url_var, font=('Arial', 11))
        self.url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.url_entry.bind('<Return>', self.navigate_to_url)
        
        self.go_btn = tk.Button(address_frame, text="Go", command=self.navigate_to_url)
        self.go_btn.pack(side=tk.RIGHT)
        
        # Bookmarks and tools
        tools_frame = tk.Frame(toolbar, bg='#e9ecef')
        tools_frame.pack(side=tk.RIGHT, pady=5)
        
        self.bookmark_btn = tk.Button(tools_frame, text="⭐", command=self.show_bookmarks, width=3)
        self.bookmark_btn.pack(side=tk.LEFT, padx=2)
        
        self.external_btn = tk.Button(tools_frame, text="🌐", command=self.open_in_external, width=3)
        self.external_btn.pack(side=tk.LEFT, padx=2)
        
        # Create bookmarks menu
        self.create_bookmarks_menu()
        
    def create_bookmarks_menu(self):
        """Create bookmarks menu"""
        self.bookmarks_menu = tk.Menu(self.window, tearoff=0)
        
        for name, url in self.bookmarks:
            self.bookmarks_menu.add_command(
                label=name,
                command=lambda u=url: self.navigate_to(u)
            )
            
        self.bookmarks_menu.add_separator()
        self.bookmarks_menu.add_command(label="Add Bookmark", command=self.add_bookmark)
        self.bookmarks_menu.add_command(label="Manage Bookmarks", command=self.manage_bookmarks)
        
    def show_bookmarks(self):
        """Show bookmarks menu"""
        try:
            x = self.bookmark_btn.winfo_rootx()
            y = self.bookmark_btn.winfo_rooty() + self.bookmark_btn.winfo_height()
            self.bookmarks_menu.tk_popup(x, y)
        finally:
            self.bookmarks_menu.grab_release()
            
    def navigate_to_url(self, event=None):
        """Navigate to URL from address bar"""
        url = self.url_var.get().strip()
        if url:
            self.navigate_to(url)
            
    def navigate_to(self, url):
        """Navigate to a specific URL"""
        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            if '.' in url:
                url = 'https://' + url
            else:
                # Treat as search query
                url = f"https://www.google.com/search?q={urllib.parse.quote(url)}"
                
        self.current_url = url
        self.url_var.set(url)
        
        # Add to history
        if self.history_index < len(self.history) - 1:
            self.history = self.history[:self.history_index + 1]
        self.history.append(url)
        self.history_index = len(self.history) - 1
        
        # Update navigation buttons
        self.update_nav_buttons()
        
        # Load the page
        self.load_page(url)
        
    def load_page(self, url):
        """Load a web page"""
        self.status_bar.config(text=f"Loading {url}...")
        self.content_text.config(state=tk.NORMAL)
        self.content_text.delete(1.0, tk.END)
        self.content_text.insert(tk.END, "Loading...")
        self.content_text.config(state=tk.DISABLED)
        
        # Load page in background thread
        def load_thread():
            try:
                # Create request with headers
                req = urllib.request.Request(
                    url,
                    headers={
                        'User-Agent': 'Python OS Browser 1.0'
                    }
                )
                
                with urllib.request.urlopen(req, timeout=10) as response:
                    content = response.read().decode('utf-8', errors='ignore')
                    
                # Parse HTML content
                parser = SimpleHTMLParser()
                parser.feed(content)
                
                # Update UI in main thread
                self.window.after(0, lambda: self.display_content(parser.title, parser.get_text()))
                
            except Exception as e:
                error_msg = f"Failed to load page: {str(e)}"
                self.window.after(0, lambda: self.display_error(error_msg))
                
        thread = threading.Thread(target=load_thread, daemon=True)
        thread.start()
        
    def display_content(self, title, content):
        """Display page content"""
        self.content_text.config(state=tk.NORMAL)
        self.content_text.delete(1.0, tk.END)
        
        # Display title
        if title:
            self.content_text.insert(tk.END, f"Title: {title}\n\n", "title")
            self.custom_window.set_title(f"Web Browser - {title}")
        
        # Display content
        self.content_text.insert(tk.END, content)
        
        # Configure text tags
        self.content_text.tag_config("title", font=('Arial', 14, 'bold'))
        
        self.content_text.config(state=tk.DISABLED)
        self.status_bar.config(text=f"Loaded: {self.current_url}")
        
    def display_error(self, error_msg):
        """Display error message"""
        self.content_text.config(state=tk.NORMAL)
        self.content_text.delete(1.0, tk.END)
        self.content_text.insert(tk.END, f"Error: {error_msg}\n\n")
        self.content_text.insert(tk.END, "This is a simple text-based browser. ")
        self.content_text.insert(tk.END, "For full web browsing experience, use the 'Open in External Browser' button.")
        self.content_text.config(state=tk.DISABLED)
        self.status_bar.config(text="Error loading page")
        
    def load_homepage(self):
        """Load the homepage"""
        homepage_content = """Welcome to Python OS Web Browser!

This is a simple text-based web browser built into Python OS.

Features:
• Basic web page loading
• Text content extraction
• Bookmarks
• History navigation
• External browser integration

Quick Links:
• Google: https://www.google.com
• GitHub: https://www.github.com
• Python.org: https://www.python.org
• Stack Overflow: https://stackoverflow.com

Note: This browser displays text content only. For full web browsing with images, 
JavaScript, and modern web features, use the 'Open in External Browser' button.

Enter a URL in the address bar above to get started!"""

        self.content_text.config(state=tk.NORMAL)
        self.content_text.delete(1.0, tk.END)
        self.content_text.insert(tk.END, homepage_content)
        self.content_text.config(state=tk.DISABLED)
        
        self.current_url = "python-os://home"
        self.url_var.set("")
        self.status_bar.config(text="Ready")
        self.custom_window.set_title("Web Browser - Home")
        
    def go_back(self):
        """Go back in history"""
        if self.history_index > 0:
            self.history_index -= 1
            url = self.history[self.history_index]
            self.current_url = url
            self.url_var.set(url)
            self.load_page(url)
            self.update_nav_buttons()
            
    def go_forward(self):
        """Go forward in history"""
        if self.history_index < len(self.history) - 1:
            self.history_index += 1
            url = self.history[self.history_index]
            self.current_url = url
            self.url_var.set(url)
            self.load_page(url)
            self.update_nav_buttons()
            
    def refresh_page(self):
        """Refresh current page"""
        if self.current_url and self.current_url != "python-os://home":
            self.load_page(self.current_url)
        else:
            self.load_homepage()
            
    def update_nav_buttons(self):
        """Update navigation button states"""
        self.back_btn.config(state=tk.NORMAL if self.history_index > 0 else tk.DISABLED)
        self.forward_btn.config(state=tk.NORMAL if self.history_index < len(self.history) - 1 else tk.DISABLED)
        
    def open_in_external(self):
        """Open current URL in external browser"""
        if self.current_url and self.current_url != "python-os://home":
            try:
                webbrowser.open(self.current_url)
            except Exception as e:
                messagebox.showerror("Error", f"Failed to open external browser: {e}")
        else:
            messagebox.showinfo("Info", "No URL to open in external browser")
            
    def add_bookmark(self):
        """Add current page to bookmarks"""
        if self.current_url and self.current_url != "python-os://home":
            name = simpledialog.askstring("Add Bookmark", "Enter bookmark name:")
            if name:
                self.bookmarks.append((name, self.current_url))
                self.create_bookmarks_menu()  # Recreate menu
                messagebox.showinfo("Success", "Bookmark added!")
        else:
            messagebox.showinfo("Info", "No page to bookmark")
            
    def manage_bookmarks(self):
        """Open bookmark management dialog"""
        messagebox.showinfo("Info", "Bookmark management not implemented yet")
        
    def cleanup(self):
        """Cleanup when closing"""
        pass
