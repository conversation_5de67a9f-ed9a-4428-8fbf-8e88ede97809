"""
Desktop Environment - Main desktop interface with taskbar and desktop icons
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import threading
import time

from .taskbar import Taskbar
from .window import CustomWindow

class Desktop:
    def __init__(self, root, config, app_launcher):
        self.root = root
        self.config = config
        self.app_launcher = app_launcher

        self.setup_desktop()
        self.create_taskbar()
        self.create_desktop_icons()
        self.start_clock_update()

    def setup_desktop(self):
        """Setup the desktop background and layout"""
        # Create main desktop frame
        self.desktop_frame = tk.Frame(self.root, bg=self.config.get('wallpaper', '#2c3e50'))
        self.desktop_frame.pack(fill=tk.BOTH, expand=True)

        # Right-click context menu for desktop
        self.desktop_menu = tk.Menu(self.root, tearoff=0)
        self.desktop_menu.add_command(label="Open Terminal", command=lambda: self.app_launcher.launch_app('terminal'))
        self.desktop_menu.add_command(label="Open File Manager", command=lambda: self.app_launcher.launch_app('file_manager'))
        self.desktop_menu.add_command(label="Open Media Player", command=lambda: self.app_launcher.launch_app('media_player'))
        self.desktop_menu.add_separator()
        self.desktop_menu.add_command(label="Settings", command=lambda: self.app_launcher.launch_app('settings'))
        self.desktop_menu.add_separator()
        self.desktop_menu.add_command(label="About Python OS", command=self.show_about)

        # Bind right-click to desktop
        self.desktop_frame.bind("<Button-3>", self.show_desktop_menu)

    def create_taskbar(self):
        """Create the taskbar"""
        self.taskbar = Taskbar(self.root, self.app_launcher)

    def create_desktop_icons(self):
        """Create desktop icons"""
        if not self.config.get('desktop_icons', True):
            return

        # Desktop icons frame
        self.icons_frame = tk.Frame(self.desktop_frame, bg=self.config.get('wallpaper', '#2c3e50'))
        self.icons_frame.pack(side=tk.LEFT, anchor=tk.NW, padx=20, pady=20)

        # Define desktop applications
        desktop_apps = [
            ("File Manager", "file_manager", "📁"),
            ("Web Browser", "browser", "🌐"),
            ("Terminal", "terminal", "💻"),
            ("Text Editor", "text_editor", "📝"),
            ("Media Player", "media_player", "🎵"),
            ("Calculator", "calculator", "🔢"),
            ("System Monitor", "system_monitor", "📊"),
            ("Settings", "settings", "⚙️")
        ]

        # Create icon buttons
        for i, (name, app_id, icon) in enumerate(desktop_apps):
            icon_frame = tk.Frame(self.icons_frame, bg=self.config.get('wallpaper', '#2c3e50'))
            icon_frame.pack(pady=10)

            # Icon button
            icon_btn = tk.Button(
                icon_frame,
                text=icon,
                font=("Arial", 24),
                bg='#34495e',
                fg='white',
                relief=tk.FLAT,
                width=3,
                height=1,
                command=lambda app=app_id: self.app_launcher.launch_app(app)
            )
            icon_btn.pack()

            # Icon label
            label = tk.Label(
                icon_frame,
                text=name,
                font=("Arial", 9),
                bg=self.config.get('wallpaper', '#2c3e50'),
                fg='white'
            )
            label.pack()

            # Hover effects
            def on_enter(e, btn=icon_btn):
                btn.config(bg='#3498db')

            def on_leave(e, btn=icon_btn):
                btn.config(bg='#34495e')

            icon_btn.bind("<Enter>", on_enter)
            icon_btn.bind("<Leave>", on_leave)

    def show_desktop_menu(self, event):
        """Show desktop context menu"""
        try:
            self.desktop_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.desktop_menu.grab_release()

    def show_about(self):
        """Show about dialog"""
        about_text = """Python OS v1.0

A Linux-like desktop environment built in Python
Running on Windows

Features:
• File Manager
• Web Browser
• Terminal Emulator
• Text Editor
• System Settings

Keyboard Shortcuts:
• Ctrl+Alt+T - Terminal
• Ctrl+Alt+F - File Manager
• Ctrl+Alt+B - Browser
• Alt+F4 - Exit

Created with Python and Tkinter"""

        messagebox.showinfo("About Python OS", about_text)

    def start_clock_update(self):
        """Start the clock update thread"""
        def update_clock():
            while True:
                try:
                    current_time = datetime.now().strftime("%H:%M:%S")
                    current_date = datetime.now().strftime("%Y-%m-%d")
                    if hasattr(self, 'taskbar'):
                        self.taskbar.update_clock(current_time, current_date)
                    time.sleep(1)
                except:
                    break

        clock_thread = threading.Thread(target=update_clock, daemon=True)
        clock_thread.start()

    def refresh_desktop(self):
        """Refresh desktop icons and layout"""
        # Reload configuration
        try:
            import json
            with open('config/settings.json', 'r') as f:
                self.config = json.load(f)
        except:
            pass

        # Update desktop background
        self.desktop_frame.config(bg=self.config.get('wallpaper', '#2c3e50'))

        # Recreate icons if needed
        if hasattr(self, 'icons_frame'):
            self.icons_frame.destroy()
            self.create_desktop_icons()
