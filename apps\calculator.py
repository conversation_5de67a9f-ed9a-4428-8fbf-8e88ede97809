"""
Calculator Application - Advanced calculator with scientific functions
"""

import tkinter as tk
from tkinter import ttk, messagebox
import math
import re

from ui.window import CustomWindow

class Calculator:
    def __init__(self, parent, window_manager, window_id):
        self.parent = parent
        self.window_manager = window_manager
        self.window_id = window_id
        
        self.current_input = ""
        self.result = 0
        self.operation = None
        self.history = []
        self.memory = 0
        self.mode = "standard"  # standard, scientific, programmer
        
        self.create_window()
        self.create_interface()
        
    def create_window(self):
        """Create the calculator window"""
        self.custom_window = CustomWindow(
            self.parent,
            title="Calculator",
            width=400,
            height=600
        )
        self.window = self.custom_window.get_window()
        self.content_frame = self.custom_window.get_content_frame()
        
    def create_interface(self):
        """Create the calculator interface"""
        # Menu bar
        self.create_menu()
        
        # Display area
        self.create_display()
        
        # Mode selector
        self.create_mode_selector()
        
        # Button area
        self.create_buttons()
        
        # History panel
        self.create_history_panel()
        
    def create_menu(self):
        """Create menu bar"""
        menubar = tk.Menu(self.window)
        self.window.config(menu=menubar)
        
        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Standard", command=lambda: self.set_mode("standard"))
        view_menu.add_command(label="Scientific", command=lambda: self.set_mode("scientific"))
        view_menu.add_command(label="Programmer", command=lambda: self.set_mode("programmer"))
        view_menu.add_separator()
        view_menu.add_command(label="History", command=self.toggle_history)
        
        # Edit menu
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Copy", command=self.copy_result)
        edit_menu.add_command(label="Paste", command=self.paste_input)
        edit_menu.add_separator()
        edit_menu.add_command(label="Clear History", command=self.clear_history)
        
    def create_display(self):
        """Create display area"""
        display_frame = tk.Frame(self.content_frame, bg='black', height=100)
        display_frame.pack(fill=tk.X, padx=5, pady=5)
        display_frame.pack_propagate(False)
        
        # Main display
        self.display_var = tk.StringVar(value="0")
        self.display = tk.Label(
            display_frame,
            textvariable=self.display_var,
            font=("Arial", 24, "bold"),
            bg='black',
            fg='white',
            anchor='e',
            padx=10
        )
        self.display.pack(fill=tk.BOTH, expand=True)
        
        # Secondary display for operations
        self.operation_var = tk.StringVar(value="")
        self.operation_display = tk.Label(
            display_frame,
            textvariable=self.operation_var,
            font=("Arial", 12),
            bg='black',
            fg='gray',
            anchor='e',
            padx=10
        )
        self.operation_display.pack(fill=tk.X)
        
    def create_mode_selector(self):
        """Create mode selector"""
        mode_frame = tk.Frame(self.content_frame)
        mode_frame.pack(fill=tk.X, padx=5, pady=2)
        
        self.mode_var = tk.StringVar(value="standard")
        
        modes = [("Standard", "standard"), ("Scientific", "scientific"), ("Programmer", "programmer")]
        
        for text, mode in modes:
            tk.Radiobutton(
                mode_frame,
                text=text,
                variable=self.mode_var,
                value=mode,
                command=lambda m=mode: self.set_mode(m)
            ).pack(side=tk.LEFT, padx=5)
            
    def create_buttons(self):
        """Create calculator buttons"""
        self.button_frame = tk.Frame(self.content_frame)
        self.button_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.create_standard_buttons()
        
    def create_standard_buttons(self):
        """Create standard calculator buttons"""
        # Clear existing buttons
        for widget in self.button_frame.winfo_children():
            widget.destroy()
            
        # Memory buttons
        memory_frame = tk.Frame(self.button_frame)
        memory_frame.pack(fill=tk.X, pady=2)
        
        memory_buttons = [
            ("MC", self.memory_clear),
            ("MR", self.memory_recall),
            ("M+", self.memory_add),
            ("M-", self.memory_subtract),
            ("MS", self.memory_store)
        ]
        
        for text, command in memory_buttons:
            btn = tk.Button(
                memory_frame,
                text=text,
                command=command,
                width=6,
                height=1,
                font=("Arial", 10)
            )
            btn.pack(side=tk.LEFT, padx=1, pady=1, fill=tk.X, expand=True)
            
        # Main buttons
        main_frame = tk.Frame(self.button_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, pady=2)
        
        if self.mode == "standard":
            self.create_standard_layout(main_frame)
        elif self.mode == "scientific":
            self.create_scientific_layout(main_frame)
        elif self.mode == "programmer":
            self.create_programmer_layout(main_frame)
            
    def create_standard_layout(self, parent):
        """Create standard calculator layout"""
        buttons = [
            [("C", self.clear), ("CE", self.clear_entry), ("⌫", self.backspace), ("÷", lambda: self.operation_click("/"))],
            [("7", lambda: self.number_click("7")), ("8", lambda: self.number_click("8")), ("9", lambda: self.number_click("9")), ("×", lambda: self.operation_click("*"))],
            [("4", lambda: self.number_click("4")), ("5", lambda: self.number_click("5")), ("6", lambda: self.number_click("6")), ("-", lambda: self.operation_click("-"))],
            [("1", lambda: self.number_click("1")), ("2", lambda: self.number_click("2")), ("3", lambda: self.number_click("3")), ("+", lambda: self.operation_click("+"))],
            [("±", self.negate), ("0", lambda: self.number_click("0")), (".", lambda: self.number_click(".")), ("=", self.equals)]
        ]
        
        for i, row in enumerate(buttons):
            row_frame = tk.Frame(parent)
            row_frame.pack(fill=tk.X, pady=1)
            
            for text, command in row:
                btn = tk.Button(
                    row_frame,
                    text=text,
                    command=command,
                    width=8,
                    height=2,
                    font=("Arial", 12, "bold")
                )
                
                # Color coding
                if text in ["C", "CE", "⌫"]:
                    btn.config(bg='#ff6b6b', fg='white')
                elif text in ["÷", "×", "-", "+", "="]:
                    btn.config(bg='#4ecdc4', fg='white')
                elif text in ["±", "."]:
                    btn.config(bg='#95a5a6', fg='white')
                else:
                    btn.config(bg='#ecf0f1', fg='black')
                    
                btn.pack(side=tk.LEFT, padx=1, fill=tk.X, expand=True)
                
    def create_scientific_layout(self, parent):
        """Create scientific calculator layout"""
        # Scientific functions row
        sci_frame = tk.Frame(parent)
        sci_frame.pack(fill=tk.X, pady=1)
        
        sci_buttons = [
            ("sin", lambda: self.function_click("sin")),
            ("cos", lambda: self.function_click("cos")),
            ("tan", lambda: self.function_click("tan")),
            ("log", lambda: self.function_click("log")),
            ("ln", lambda: self.function_click("ln"))
        ]
        
        for text, command in sci_buttons:
            btn = tk.Button(
                sci_frame,
                text=text,
                command=command,
                width=6,
                height=1,
                font=("Arial", 10),
                bg='#9b59b6',
                fg='white'
            )
            btn.pack(side=tk.LEFT, padx=1, fill=tk.X, expand=True)
            
        # Second row of scientific functions
        sci_frame2 = tk.Frame(parent)
        sci_frame2.pack(fill=tk.X, pady=1)
        
        sci_buttons2 = [
            ("√", lambda: self.function_click("sqrt")),
            ("x²", lambda: self.function_click("square")),
            ("x³", lambda: self.function_click("cube")),
            ("xʸ", lambda: self.operation_click("**")),
            ("π", lambda: self.constant_click("pi"))
        ]
        
        for text, command in sci_buttons2:
            btn = tk.Button(
                sci_frame2,
                text=text,
                command=command,
                width=6,
                height=1,
                font=("Arial", 10),
                bg='#9b59b6',
                fg='white'
            )
            btn.pack(side=tk.LEFT, padx=1, fill=tk.X, expand=True)
            
        # Standard layout below
        self.create_standard_layout(parent)
        
    def create_programmer_layout(self, parent):
        """Create programmer calculator layout"""
        # Number system selector
        base_frame = tk.Frame(parent)
        base_frame.pack(fill=tk.X, pady=2)
        
        self.base_var = tk.StringVar(value="DEC")
        
        bases = [("HEX", "HEX"), ("DEC", "DEC"), ("OCT", "OCT"), ("BIN", "BIN")]
        
        for text, base in bases:
            tk.Radiobutton(
                base_frame,
                text=text,
                variable=self.base_var,
                value=base,
                command=self.base_changed
            ).pack(side=tk.LEFT, padx=5)
            
        # Hex buttons (A-F)
        hex_frame = tk.Frame(parent)
        hex_frame.pack(fill=tk.X, pady=1)
        
        hex_buttons = ["A", "B", "C", "D", "E", "F"]
        
        for letter in hex_buttons:
            btn = tk.Button(
                hex_frame,
                text=letter,
                command=lambda l=letter: self.hex_click(l),
                width=6,
                height=1,
                font=("Arial", 10),
                bg='#e67e22',
                fg='white'
            )
            btn.pack(side=tk.LEFT, padx=1, fill=tk.X, expand=True)
            
        # Bitwise operations
        bitwise_frame = tk.Frame(parent)
        bitwise_frame.pack(fill=tk.X, pady=1)
        
        bitwise_buttons = [
            ("AND", lambda: self.operation_click("&")),
            ("OR", lambda: self.operation_click("|")),
            ("XOR", lambda: self.operation_click("^")),
            ("NOT", lambda: self.function_click("not")),
            ("<<", lambda: self.operation_click("<<")),
            (">>", lambda: self.operation_click(">>"))
        ]
        
        for text, command in bitwise_buttons:
            btn = tk.Button(
                bitwise_frame,
                text=text,
                command=command,
                width=6,
                height=1,
                font=("Arial", 9),
                bg='#34495e',
                fg='white'
            )
            btn.pack(side=tk.LEFT, padx=1, fill=tk.X, expand=True)
            
        # Standard layout below
        self.create_standard_layout(parent)
        
    def create_history_panel(self):
        """Create history panel"""
        self.history_frame = tk.Frame(self.content_frame)
        # Initially hidden
        
        tk.Label(self.history_frame, text="History", font=("Arial", 12, "bold")).pack(pady=5)
        
        # History listbox
        history_list_frame = tk.Frame(self.history_frame)
        history_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.history_listbox = tk.Listbox(history_list_frame)
        history_scrollbar = ttk.Scrollbar(history_list_frame, orient=tk.VERTICAL, command=self.history_listbox.yview)
        self.history_listbox.configure(yscrollcommand=history_scrollbar.set)
        
        self.history_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # History controls
        history_controls = tk.Frame(self.history_frame)
        history_controls.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Button(history_controls, text="Use", command=self.use_history_item).pack(side=tk.LEFT, padx=2)
        tk.Button(history_controls, text="Clear", command=self.clear_history).pack(side=tk.LEFT, padx=2)
        
    def number_click(self, number):
        """Handle number button clicks"""
        if self.current_input == "0" or self.current_input == "Error":
            self.current_input = number
        else:
            self.current_input += number
        self.update_display()
        
    def operation_click(self, op):
        """Handle operation button clicks"""
        try:
            if self.current_input:
                if self.operation and self.result is not None:
                    self.calculate()
                else:
                    self.result = float(self.current_input)
                    
                self.operation = op
                self.operation_var.set(f"{self.result} {op}")
                self.current_input = ""
        except:
            self.display_error()
            
    def function_click(self, func):
        """Handle function button clicks"""
        try:
            if not self.current_input:
                return
                
            value = float(self.current_input)
            
            if func == "sin":
                result = math.sin(math.radians(value))
            elif func == "cos":
                result = math.cos(math.radians(value))
            elif func == "tan":
                result = math.tan(math.radians(value))
            elif func == "log":
                result = math.log10(value)
            elif func == "ln":
                result = math.log(value)
            elif func == "sqrt":
                result = math.sqrt(value)
            elif func == "square":
                result = value ** 2
            elif func == "cube":
                result = value ** 3
            elif func == "not":
                result = ~int(value)
            else:
                return
                
            self.current_input = str(result)
            self.add_to_history(f"{func}({value}) = {result}")
            self.update_display()
            
        except Exception as e:
            self.display_error()
            
    def constant_click(self, constant):
        """Handle constant button clicks"""
        if constant == "pi":
            self.current_input = str(math.pi)
        elif constant == "e":
            self.current_input = str(math.e)
        self.update_display()
        
    def hex_click(self, letter):
        """Handle hex letter clicks"""
        if self.base_var.get() == "HEX":
            self.number_click(letter)
            
    def equals(self):
        """Handle equals button"""
        try:
            if self.operation and self.current_input:
                self.calculate()
                self.operation = None
                self.operation_var.set("")
        except:
            self.display_error()
            
    def calculate(self):
        """Perform calculation"""
        try:
            if not self.operation or not self.current_input:
                return
                
            current_value = float(self.current_input)
            expression = f"{self.result} {self.operation} {current_value}"
            
            if self.operation == "+":
                result = self.result + current_value
            elif self.operation == "-":
                result = self.result - current_value
            elif self.operation == "*":
                result = self.result * current_value
            elif self.operation == "/":
                if current_value == 0:
                    raise ZeroDivisionError("Division by zero")
                result = self.result / current_value
            elif self.operation == "**":
                result = self.result ** current_value
            elif self.operation == "&":
                result = int(self.result) & int(current_value)
            elif self.operation == "|":
                result = int(self.result) | int(current_value)
            elif self.operation == "^":
                result = int(self.result) ^ int(current_value)
            elif self.operation == "<<":
                result = int(self.result) << int(current_value)
            elif self.operation == ">>":
                result = int(self.result) >> int(current_value)
            else:
                return
                
            self.result = result
            self.current_input = str(result)
            self.add_to_history(f"{expression} = {result}")
            self.update_display()
            
        except Exception as e:
            self.display_error()
            
    def clear(self):
        """Clear all"""
        self.current_input = "0"
        self.result = 0
        self.operation = None
        self.operation_var.set("")
        self.update_display()
        
    def clear_entry(self):
        """Clear current entry"""
        self.current_input = "0"
        self.update_display()
        
    def backspace(self):
        """Remove last character"""
        if len(self.current_input) > 1:
            self.current_input = self.current_input[:-1]
        else:
            self.current_input = "0"
        self.update_display()
        
    def negate(self):
        """Negate current number"""
        try:
            if self.current_input and self.current_input != "0":
                if self.current_input.startswith("-"):
                    self.current_input = self.current_input[1:]
                else:
                    self.current_input = "-" + self.current_input
                self.update_display()
        except:
            pass
            
    def memory_clear(self):
        """Clear memory"""
        self.memory = 0
        
    def memory_recall(self):
        """Recall from memory"""
        self.current_input = str(self.memory)
        self.update_display()
        
    def memory_add(self):
        """Add to memory"""
        try:
            self.memory += float(self.current_input)
        except:
            pass
            
    def memory_subtract(self):
        """Subtract from memory"""
        try:
            self.memory -= float(self.current_input)
        except:
            pass
            
    def memory_store(self):
        """Store in memory"""
        try:
            self.memory = float(self.current_input)
        except:
            pass
            
    def set_mode(self, mode):
        """Set calculator mode"""
        self.mode = mode
        self.mode_var.set(mode)
        self.create_buttons()
        
    def base_changed(self):
        """Handle number base change"""
        try:
            if self.current_input and self.current_input != "0":
                # Convert current number to new base
                current_decimal = int(float(self.current_input))
                
                base = self.base_var.get()
                if base == "HEX":
                    self.current_input = hex(current_decimal)[2:].upper()
                elif base == "OCT":
                    self.current_input = oct(current_decimal)[2:]
                elif base == "BIN":
                    self.current_input = bin(current_decimal)[2:]
                else:  # DEC
                    self.current_input = str(current_decimal)
                    
                self.update_display()
        except:
            pass
            
    def update_display(self):
        """Update the display"""
        display_text = self.current_input
        
        # Format based on current base
        if self.mode == "programmer":
            base = self.base_var.get()
            if base != "DEC":
                display_text = f"{base}: {display_text}"
                
        self.display_var.set(display_text)
        
    def display_error(self):
        """Display error"""
        self.current_input = "Error"
        self.update_display()
        
    def add_to_history(self, entry):
        """Add entry to history"""
        self.history.append(entry)
        self.history_listbox.insert(tk.END, entry)
        self.history_listbox.see(tk.END)
        
    def toggle_history(self):
        """Toggle history panel visibility"""
        if self.history_frame.winfo_viewable():
            self.history_frame.pack_forget()
        else:
            self.history_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=5)
            
    def use_history_item(self):
        """Use selected history item"""
        selection = self.history_listbox.curselection()
        if selection:
            item = self.history_listbox.get(selection[0])
            # Extract result from history item
            if "=" in item:
                result = item.split("=")[-1].strip()
                self.current_input = result
                self.update_display()
                
    def clear_history(self):
        """Clear calculation history"""
        self.history.clear()
        self.history_listbox.delete(0, tk.END)
        
    def copy_result(self):
        """Copy result to clipboard"""
        try:
            self.window.clipboard_clear()
            self.window.clipboard_append(self.current_input)
        except:
            pass
            
    def paste_input(self):
        """Paste from clipboard"""
        try:
            clipboard_text = self.window.clipboard_get()
            # Validate that it's a number
            float(clipboard_text)
            self.current_input = clipboard_text
            self.update_display()
        except:
            pass
            
    def cleanup(self):
        """Cleanup when closing"""
        pass
