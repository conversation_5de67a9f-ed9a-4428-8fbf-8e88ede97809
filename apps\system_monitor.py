"""
System Monitor Application - Real-time system monitoring with graphs and statistics
"""

import tkinter as tk
from tkinter import ttk, messagebox
import psutil
import threading
import time
from datetime import datetime, timedelta
import json

from ui.window import CustomWindow

try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    from matplotlib.figure import Figure
    import matplotlib.animation as animation
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

class SystemMonitor:
    def __init__(self, parent, window_manager, window_id):
        self.parent = parent
        self.window_manager = window_manager
        self.window_id = window_id
        
        self.monitoring = False
        self.data_points = 100  # Number of data points to keep
        self.update_interval = 1000  # Update every 1 second
        
        # Data storage
        self.cpu_data = []
        self.memory_data = []
        self.disk_data = []
        self.network_data = []
        self.timestamps = []
        
        self.create_window()
        self.create_interface()
        self.start_monitoring()
        
    def create_window(self):
        """Create the system monitor window"""
        self.custom_window = CustomWindow(
            self.parent,
            title="System Monitor",
            width=1000,
            height=700
        )
        self.window = self.custom_window.get_window()
        self.content_frame = self.custom_window.get_content_frame()
        
        # Override close behavior
        self.custom_window.on_close = self.on_close
        
    def create_interface(self):
        """Create the system monitor interface"""
        # Create notebook for different monitoring tabs
        self.notebook = ttk.Notebook(self.content_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Overview tab
        self.create_overview_tab()
        
        # Processes tab
        self.create_processes_tab()
        
        # Performance tab
        if MATPLOTLIB_AVAILABLE:
            self.create_performance_tab()
        
        # Network tab
        self.create_network_tab()
        
        # Disk tab
        self.create_disk_tab()
        
        # System info tab
        self.create_system_info_tab()
        
        # Control buttons
        self.create_control_buttons()
        
    def create_overview_tab(self):
        """Create overview tab with key metrics"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Overview")
        
        # CPU section
        cpu_frame = tk.LabelFrame(frame, text="CPU Usage", padx=10, pady=10)
        cpu_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.cpu_label = tk.Label(cpu_frame, text="CPU: 0%", font=("Arial", 14))
        self.cpu_label.pack(side=tk.LEFT)
        
        self.cpu_progress = ttk.Progressbar(cpu_frame, length=300, mode='determinate')
        self.cpu_progress.pack(side=tk.RIGHT, padx=10)
        
        # Memory section
        memory_frame = tk.LabelFrame(frame, text="Memory Usage", padx=10, pady=10)
        memory_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.memory_label = tk.Label(memory_frame, text="Memory: 0%", font=("Arial", 14))
        self.memory_label.pack(side=tk.LEFT)
        
        self.memory_progress = ttk.Progressbar(memory_frame, length=300, mode='determinate')
        self.memory_progress.pack(side=tk.RIGHT, padx=10)
        
        # Disk section
        disk_frame = tk.LabelFrame(frame, text="Disk Usage", padx=10, pady=10)
        disk_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.disk_label = tk.Label(disk_frame, text="Disk: 0%", font=("Arial", 14))
        self.disk_label.pack(side=tk.LEFT)
        
        self.disk_progress = ttk.Progressbar(disk_frame, length=300, mode='determinate')
        self.disk_progress.pack(side=tk.RIGHT, padx=10)
        
        # Network section
        network_frame = tk.LabelFrame(frame, text="Network Activity", padx=10, pady=10)
        network_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.network_label = tk.Label(network_frame, text="Network: 0 KB/s", font=("Arial", 14))
        self.network_label.pack()
        
        # System uptime
        uptime_frame = tk.LabelFrame(frame, text="System Information", padx=10, pady=10)
        uptime_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.uptime_label = tk.Label(uptime_frame, text="Uptime: 0:00:00", font=("Arial", 12))
        self.uptime_label.pack()
        
        self.boot_time_label = tk.Label(uptime_frame, text="Boot time: Unknown", font=("Arial", 12))
        self.boot_time_label.pack()
        
    def create_processes_tab(self):
        """Create processes tab with running processes list"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Processes")
        
        # Control buttons
        control_frame = tk.Frame(frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Button(control_frame, text="Refresh", command=self.refresh_processes).pack(side=tk.LEFT, padx=5)
        tk.Button(control_frame, text="End Process", command=self.end_process).pack(side=tk.LEFT, padx=5)
        
        # Search frame
        search_frame = tk.Frame(frame)
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(search_frame, text="Search:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        self.search_entry = tk.Entry(search_frame, textvariable=self.search_var)
        self.search_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.search_entry.bind('<KeyRelease>', self.filter_processes)
        
        # Processes tree
        tree_frame = tk.Frame(frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        columns = ('PID', 'Name', 'CPU%', 'Memory%', 'Status')
        self.process_tree = ttk.Treeview(tree_frame, columns=columns, show='headings')
        
        for col in columns:
            self.process_tree.heading(col, text=col)
            if col == 'PID':
                self.process_tree.column(col, width=80)
            elif col == 'Name':
                self.process_tree.column(col, width=200)
            else:
                self.process_tree.column(col, width=100)
        
        # Scrollbars for process tree
        v_scroll = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.process_tree.yview)
        h_scroll = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.process_tree.xview)
        self.process_tree.configure(yscrollcommand=v_scroll.set, xscrollcommand=h_scroll.set)
        
        self.process_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        h_scroll.pack(side=tk.BOTTOM, fill=tk.X)
        
    def create_performance_tab(self):
        """Create performance tab with graphs"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Performance")
        
        # Create matplotlib figure
        self.fig = Figure(figsize=(12, 8), dpi=100)
        
        # CPU subplot
        self.cpu_subplot = self.fig.add_subplot(2, 2, 1)
        self.cpu_subplot.set_title('CPU Usage (%)')
        self.cpu_subplot.set_ylim(0, 100)
        
        # Memory subplot
        self.memory_subplot = self.fig.add_subplot(2, 2, 2)
        self.memory_subplot.set_title('Memory Usage (%)')
        self.memory_subplot.set_ylim(0, 100)
        
        # Disk subplot
        self.disk_subplot = self.fig.add_subplot(2, 2, 3)
        self.disk_subplot.set_title('Disk Usage (%)')
        self.disk_subplot.set_ylim(0, 100)
        
        # Network subplot
        self.network_subplot = self.fig.add_subplot(2, 2, 4)
        self.network_subplot.set_title('Network Activity (KB/s)')
        
        # Create canvas
        self.canvas = FigureCanvasTkAgg(self.fig, frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
    def create_network_tab(self):
        """Create network tab with network information"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Network")
        
        # Network interfaces
        interfaces_frame = tk.LabelFrame(frame, text="Network Interfaces", padx=10, pady=10)
        interfaces_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Create treeview for network interfaces
        net_columns = ('Interface', 'Status', 'Bytes Sent', 'Bytes Received', 'Packets Sent', 'Packets Received')
        self.network_tree = ttk.Treeview(interfaces_frame, columns=net_columns, show='headings')
        
        for col in net_columns:
            self.network_tree.heading(col, text=col)
            self.network_tree.column(col, width=120)
        
        self.network_tree.pack(fill=tk.BOTH, expand=True)
        
        # Network statistics
        stats_frame = tk.LabelFrame(frame, text="Network Statistics", padx=10, pady=10)
        stats_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.network_stats_label = tk.Label(stats_frame, text="Loading...", justify=tk.LEFT)
        self.network_stats_label.pack()
        
    def create_disk_tab(self):
        """Create disk tab with disk information"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Disk")
        
        # Disk usage
        usage_frame = tk.LabelFrame(frame, text="Disk Usage", padx=10, pady=10)
        usage_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Create treeview for disk usage
        disk_columns = ('Drive', 'Total', 'Used', 'Free', 'Usage%', 'File System')
        self.disk_tree = ttk.Treeview(usage_frame, columns=disk_columns, show='headings')
        
        for col in disk_columns:
            self.disk_tree.heading(col, text=col)
            self.disk_tree.column(col, width=100)
        
        self.disk_tree.pack(fill=tk.BOTH, expand=True)
        
        # Disk I/O statistics
        io_frame = tk.LabelFrame(frame, text="Disk I/O Statistics", padx=10, pady=10)
        io_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.disk_io_label = tk.Label(io_frame, text="Loading...", justify=tk.LEFT)
        self.disk_io_label.pack()
        
    def create_system_info_tab(self):
        """Create system information tab"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="System Info")
        
        # Create scrolled text for system info
        info_frame = tk.Frame(frame)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.system_info_text = tk.Text(info_frame, wrap=tk.WORD, state=tk.DISABLED)
        
        scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.system_info_text.yview)
        self.system_info_text.configure(yscrollcommand=scrollbar.set)
        
        self.system_info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Load system information
        self.load_system_info()
        
    def create_control_buttons(self):
        """Create control buttons"""
        control_frame = tk.Frame(self.content_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.start_stop_btn = tk.Button(
            control_frame,
            text="Stop Monitoring",
            command=self.toggle_monitoring,
            bg='#e74c3c',
            fg='white'
        )
        self.start_stop_btn.pack(side=tk.LEFT, padx=5)
        
        tk.Button(
            control_frame,
            text="Export Data",
            command=self.export_data
        ).pack(side=tk.LEFT, padx=5)
        
        tk.Button(
            control_frame,
            text="Clear Data",
            command=self.clear_data
        ).pack(side=tk.LEFT, padx=5)
        
        # Update interval
        tk.Label(control_frame, text="Update Interval (ms):").pack(side=tk.RIGHT, padx=5)
        self.interval_var = tk.StringVar(value=str(self.update_interval))
        interval_entry = tk.Entry(control_frame, textvariable=self.interval_var, width=10)
        interval_entry.pack(side=tk.RIGHT, padx=5)
        interval_entry.bind('<Return>', self.update_interval_changed)
        
    def start_monitoring(self):
        """Start system monitoring"""
        self.monitoring = True
        self.update_data()
        
    def stop_monitoring(self):
        """Stop system monitoring"""
        self.monitoring = False
        
    def toggle_monitoring(self):
        """Toggle monitoring on/off"""
        if self.monitoring:
            self.stop_monitoring()
            self.start_stop_btn.config(text="Start Monitoring", bg='#27ae60')
        else:
            self.start_monitoring()
            self.start_stop_btn.config(text="Stop Monitoring", bg='#e74c3c')
            
    def update_data(self):
        """Update system data"""
        if not self.monitoring:
            return
            
        try:
            # Get current timestamp
            current_time = datetime.now()
            
            # Get system metrics
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Update data lists
            self.cpu_data.append(cpu_percent)
            self.memory_data.append(memory.percent)
            self.disk_data.append(disk.percent)
            self.timestamps.append(current_time)
            
            # Keep only recent data points
            if len(self.cpu_data) > self.data_points:
                self.cpu_data.pop(0)
                self.memory_data.pop(0)
                self.disk_data.pop(0)
                self.timestamps.pop(0)
            
            # Update overview tab
            self.update_overview(cpu_percent, memory, disk)
            
            # Update performance graphs
            if MATPLOTLIB_AVAILABLE:
                self.update_graphs()
            
            # Update other tabs
            self.update_network_info()
            self.update_disk_info()
            
        except Exception as e:
            print(f"Error updating system data: {e}")
        
        # Schedule next update
        if self.monitoring:
            self.window.after(self.update_interval, self.update_data)
            
    def update_overview(self, cpu_percent, memory, disk):
        """Update overview tab"""
        # CPU
        self.cpu_label.config(text=f"CPU: {cpu_percent:.1f}%")
        self.cpu_progress['value'] = cpu_percent
        
        # Memory
        memory_gb = memory.total / (1024**3)
        used_gb = memory.used / (1024**3)
        self.memory_label.config(text=f"Memory: {memory.percent:.1f}% ({used_gb:.1f}/{memory_gb:.1f} GB)")
        self.memory_progress['value'] = memory.percent
        
        # Disk
        disk_gb = disk.total / (1024**3)
        used_disk_gb = disk.used / (1024**3)
        self.disk_label.config(text=f"Disk: {disk.percent:.1f}% ({used_disk_gb:.1f}/{disk_gb:.1f} GB)")
        self.disk_progress['value'] = disk.percent
        
        # Uptime
        try:
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            uptime = datetime.now() - boot_time
            self.uptime_label.config(text=f"Uptime: {str(uptime).split('.')[0]}")
            self.boot_time_label.config(text=f"Boot time: {boot_time.strftime('%Y-%m-%d %H:%M:%S')}")
        except:
            pass
            
    def update_graphs(self):
        """Update performance graphs"""
        if not self.timestamps:
            return
            
        # Clear subplots
        self.cpu_subplot.clear()
        self.memory_subplot.clear()
        self.disk_subplot.clear()
        self.network_subplot.clear()
        
        # Plot data
        times = [(t - self.timestamps[0]).total_seconds() for t in self.timestamps]
        
        self.cpu_subplot.plot(times, self.cpu_data, 'b-')
        self.cpu_subplot.set_title('CPU Usage (%)')
        self.cpu_subplot.set_ylim(0, 100)
        
        self.memory_subplot.plot(times, self.memory_data, 'r-')
        self.memory_subplot.set_title('Memory Usage (%)')
        self.memory_subplot.set_ylim(0, 100)
        
        self.disk_subplot.plot(times, self.disk_data, 'g-')
        self.disk_subplot.set_title('Disk Usage (%)')
        self.disk_subplot.set_ylim(0, 100)
        
        # Network data (placeholder)
        if len(self.network_data) > 0:
            self.network_subplot.plot(times[-len(self.network_data):], self.network_data, 'm-')
        self.network_subplot.set_title('Network Activity (KB/s)')
        
        # Refresh canvas
        self.canvas.draw()
        
    def refresh_processes(self):
        """Refresh process list"""
        # Clear existing items
        for item in self.process_tree.get_children():
            self.process_tree.delete(item)
            
        try:
            # Get all processes
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            # Sort by CPU usage
            processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)
            
            # Add to tree
            for proc in processes[:100]:  # Limit to top 100 processes
                self.process_tree.insert('', 'end', values=(
                    proc.get('pid', 'N/A'),
                    proc.get('name', 'N/A'),
                    f"{proc.get('cpu_percent', 0):.1f}",
                    f"{proc.get('memory_percent', 0):.1f}",
                    proc.get('status', 'N/A')
                ))
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh processes: {e}")
            
    def filter_processes(self, event=None):
        """Filter processes based on search term"""
        search_term = self.search_var.get().lower()
        
        # Clear and repopulate based on filter
        for item in self.process_tree.get_children():
            values = self.process_tree.item(item)['values']
            if search_term in values[1].lower():  # Search in process name
                self.process_tree.reattach(item, '', 'end')
            else:
                self.process_tree.detach(item)
                
    def end_process(self):
        """End selected process"""
        selection = self.process_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a process to end")
            return
            
        item = self.process_tree.item(selection[0])
        pid = item['values'][0]
        name = item['values'][1]
        
        if messagebox.askyesno("Confirm", f"Are you sure you want to end process '{name}' (PID: {pid})?"):
            try:
                proc = psutil.Process(int(pid))
                proc.terminate()
                messagebox.showinfo("Success", f"Process {name} terminated successfully")
                self.refresh_processes()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to terminate process: {e}")
                
    def update_network_info(self):
        """Update network information"""
        try:
            # Clear existing items
            for item in self.network_tree.get_children():
                self.network_tree.delete(item)
                
            # Get network interfaces
            net_io = psutil.net_io_counters(pernic=True)
            
            for interface, stats in net_io.items():
                self.network_tree.insert('', 'end', values=(
                    interface,
                    "Active",
                    f"{stats.bytes_sent:,}",
                    f"{stats.bytes_recv:,}",
                    f"{stats.packets_sent:,}",
                    f"{stats.packets_recv:,}"
                ))
                
            # Update network statistics
            total_stats = psutil.net_io_counters()
            stats_text = f"""Total Bytes Sent: {total_stats.bytes_sent:,}
Total Bytes Received: {total_stats.bytes_recv:,}
Total Packets Sent: {total_stats.packets_sent:,}
Total Packets Received: {total_stats.packets_recv:,}"""
            
            self.network_stats_label.config(text=stats_text)
            
        except Exception as e:
            print(f"Error updating network info: {e}")
            
    def update_disk_info(self):
        """Update disk information"""
        try:
            # Clear existing items
            for item in self.disk_tree.get_children():
                self.disk_tree.delete(item)
                
            # Get disk partitions
            partitions = psutil.disk_partitions()
            
            for partition in partitions:
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    
                    self.disk_tree.insert('', 'end', values=(
                        partition.device,
                        f"{usage.total / (1024**3):.1f} GB",
                        f"{usage.used / (1024**3):.1f} GB",
                        f"{usage.free / (1024**3):.1f} GB",
                        f"{(usage.used / usage.total) * 100:.1f}%",
                        partition.fstype
                    ))
                except PermissionError:
                    continue
                    
            # Update disk I/O statistics
            disk_io = psutil.disk_io_counters()
            if disk_io:
                io_text = f"""Read Count: {disk_io.read_count:,}
Write Count: {disk_io.write_count:,}
Read Bytes: {disk_io.read_bytes:,}
Write Bytes: {disk_io.write_bytes:,}
Read Time: {disk_io.read_time:,} ms
Write Time: {disk_io.write_time:,} ms"""
                
                self.disk_io_label.config(text=io_text)
                
        except Exception as e:
            print(f"Error updating disk info: {e}")
            
    def load_system_info(self):
        """Load system information"""
        try:
            import platform
            
            info_text = f"""System Information:

Operating System: {platform.system()} {platform.release()}
Version: {platform.version()}
Architecture: {platform.architecture()[0]}
Machine: {platform.machine()}
Processor: {platform.processor()}
Python Version: {platform.python_version()}

CPU Information:
Physical Cores: {psutil.cpu_count(logical=False)}
Total Cores: {psutil.cpu_count(logical=True)}
Max Frequency: {psutil.cpu_freq().max:.2f} MHz
Min Frequency: {psutil.cpu_freq().min:.2f} MHz
Current Frequency: {psutil.cpu_freq().current:.2f} MHz

Memory Information:
Total: {psutil.virtual_memory().total / (1024**3):.2f} GB
Available: {psutil.virtual_memory().available / (1024**3):.2f} GB
Used: {psutil.virtual_memory().used / (1024**3):.2f} GB
Percentage: {psutil.virtual_memory().percent:.1f}%

Swap Memory:
Total: {psutil.swap_memory().total / (1024**3):.2f} GB
Used: {psutil.swap_memory().used / (1024**3):.2f} GB
Free: {psutil.swap_memory().free / (1024**3):.2f} GB
Percentage: {psutil.swap_memory().percent:.1f}%

Boot Time: {datetime.fromtimestamp(psutil.boot_time()).strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            self.system_info_text.config(state=tk.NORMAL)
            self.system_info_text.delete(1.0, tk.END)
            self.system_info_text.insert(1.0, info_text)
            self.system_info_text.config(state=tk.DISABLED)
            
        except Exception as e:
            error_text = f"Error loading system information: {e}"
            self.system_info_text.config(state=tk.NORMAL)
            self.system_info_text.delete(1.0, tk.END)
            self.system_info_text.insert(1.0, error_text)
            self.system_info_text.config(state=tk.DISABLED)
            
    def update_interval_changed(self, event=None):
        """Update monitoring interval"""
        try:
            new_interval = int(self.interval_var.get())
            if new_interval >= 100:  # Minimum 100ms
                self.update_interval = new_interval
            else:
                messagebox.showwarning("Warning", "Minimum interval is 100ms")
                self.interval_var.set(str(self.update_interval))
        except ValueError:
            messagebox.showerror("Error", "Invalid interval value")
            self.interval_var.set(str(self.update_interval))
            
    def export_data(self):
        """Export monitoring data to file"""
        try:
            from tkinter import filedialog
            
            filename = filedialog.asksaveasfilename(
                title="Export Monitoring Data",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if filename:
                data = {
                    'timestamps': [t.isoformat() for t in self.timestamps],
                    'cpu_data': self.cpu_data,
                    'memory_data': self.memory_data,
                    'disk_data': self.disk_data,
                    'network_data': self.network_data
                }
                
                with open(filename, 'w') as f:
                    json.dump(data, f, indent=2)
                    
                messagebox.showinfo("Success", f"Data exported to {filename}")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export data: {e}")
            
    def clear_data(self):
        """Clear all monitoring data"""
        if messagebox.askyesno("Confirm", "Are you sure you want to clear all monitoring data?"):
            self.cpu_data.clear()
            self.memory_data.clear()
            self.disk_data.clear()
            self.network_data.clear()
            self.timestamps.clear()
            
            if MATPLOTLIB_AVAILABLE:
                self.update_graphs()
                
    def on_close(self):
        """Handle window close"""
        self.stop_monitoring()
        return True
        
    def cleanup(self):
        """Cleanup when closing"""
        self.stop_monitoring()
