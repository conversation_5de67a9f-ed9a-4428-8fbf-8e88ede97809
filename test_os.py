#!/usr/bin/env python3
"""
Test script for Python OS - Verify all components work correctly
"""

import sys
import os
import importlib

def test_imports():
    """Test that all required modules can be imported"""
    print("Testing imports...")
    
    modules_to_test = [
        'tkinter',
        'psutil',
        'main',
        'ui.desktop',
        'ui.taskbar',
        'ui.window',
        'system.window_manager',
        'system.app_launcher',
        'apps.file_manager',
        'apps.browser',
        'apps.terminal',
        'apps.text_editor',
        'apps.settings'
    ]
    
    failed_imports = []
    
    for module in modules_to_test:
        try:
            importlib.import_module(module)
            print(f"✓ {module}")
        except ImportError as e:
            print(f"✗ {module}: {e}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\nFailed to import {len(failed_imports)} modules:")
        for module in failed_imports:
            print(f"  - {module}")
        return False
    else:
        print(f"\nAll {len(modules_to_test)} modules imported successfully!")
        return True

def test_file_structure():
    """Test that all required files exist"""
    print("\nTesting file structure...")
    
    required_files = [
        'main.py',
        'requirements.txt',
        'build.py',
        'README.md',
        'config/settings.json',
        'ui/__init__.py',
        'ui/desktop.py',
        'ui/taskbar.py',
        'ui/window.py',
        'system/__init__.py',
        'system/window_manager.py',
        'system/app_launcher.py',
        'apps/__init__.py',
        'apps/file_manager.py',
        'apps/browser.py',
        'apps/terminal.py',
        'apps/text_editor.py',
        'apps/settings.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\nMissing {len(missing_files)} files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    else:
        print(f"\nAll {len(required_files)} files found!")
        return True

def test_configuration():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        import json
        with open('config/settings.json', 'r') as f:
            config = json.load(f)
        
        required_keys = ['theme', 'wallpaper', 'taskbar_position', 'desktop_icons']
        missing_keys = [key for key in required_keys if key not in config]
        
        if missing_keys:
            print(f"✗ Missing configuration keys: {missing_keys}")
            return False
        else:
            print("✓ Configuration file is valid")
            return True
            
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_app_launcher():
    """Test app launcher functionality"""
    print("\nTesting app launcher...")
    
    try:
        from system.app_launcher import AppLauncher
        from system.window_manager import WindowManager
        import tkinter as tk
        
        # Create a minimal test environment
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        window_manager = WindowManager(root)
        app_launcher = AppLauncher(window_manager)
        
        # Test getting available apps
        available_apps = app_launcher.get_available_apps()
        expected_apps = ['file_manager', 'browser', 'terminal', 'text_editor', 'settings']
        
        missing_apps = [app for app in expected_apps if app not in available_apps]
        
        if missing_apps:
            print(f"✗ Missing apps: {missing_apps}")
            return False
        else:
            print("✓ All expected apps are available")
            
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ App launcher test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("Python OS Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("File Structure Test", test_file_structure),
        ("Configuration Test", test_configuration),
        ("App Launcher Test", test_app_launcher)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * len(test_name))
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed_tests}/{total_tests} tests passed")
    print("=" * 50)
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Python OS is ready to run.")
        print("\nTo start Python OS:")
        print("  python main.py")
        print("\nTo build executable:")
        print("  python build.py")
        return True
    else:
        print("❌ Some tests failed. Please fix the issues before running Python OS.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
